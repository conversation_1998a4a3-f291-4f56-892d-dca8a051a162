responses:
  common:
    unknown: "Desconocido"
    hub: "Hub"
  setup:
    setupComplete: "¿Listo para crear? ¡Haz clic en el botón Crear Hub!"
    editMessagePrompt: "{emoji} Por favor, usa el modal para editar tu mensaje."
    preview:
      titleSaved: "{emoji} ¡Información del hub Guardada!"
      previewTitle: "Aquí tienes una vista previa de tu hub:"
      name: "{emoji} Nombre"
      short: "{emoji} Nombre Corto"
      description: "{emoji} Descripción"
    locale:
      successTitle: "¡Éxito!"
      successDescription: "{tick} Tu idioma se ha establecido a **{locale_name}**"
    loading:
      creatingHub: "{loading} Creando Tu Hub..."
      pleaseWait: "Por favor espera mientras configuramos tu espacio comunitario."
    errors:
      hubCreationFailed: "{no} Error en la Creación del Hub"
  appeal:
    constants:
      unknownHub: "Hub Desconocido"
      noReason: "No se proporcionó razón"
    status:
      pending: "Pendiente de Respuesta"
      cooldown: "En Enfriamiento"
      canAppealAgain: "Apelable"
      canAppeal: "Apelable"
    fields:
      date: "Fecha:"
      reason: "Razón:"
    errors:
      recordFailed: "Error al registrar decisión: {error}"
      notFoundOrDeleted: "Apelación no encontrada o ha sido eliminada."
      updateFailed: "Error al actualizar apelación. Por favor intenta nuevamente más tarde."
    dm:
      accepted: "Tu apelación respecto a una acción de moderación{hubName} ha sido aceptada. Nuestro equipo revisó tu caso y determinó que tu apelación debe ser aceptada."
      declined: "Tu apelación respecto a una acción de moderación{hubName} ha sido revisada. Tras cuidadosa consideración, tu apelación ha sido declinada."
      moderatorNote: "Nota del moderador: {reason}"
    embed:
      title: "Tus Infracciones Apelables"
      description: "Selecciona una infracción abajo para enviar una apelación, o ver el estado de apelaciones existentes."
      noInfractions:
        title: "¡Impecable!"
        description: "No se encontraron infracciones apelables."
      footer:
        canAppeal: "💡 Puedes apelar {count} infracción(es). Usa el menú de selección abajo."
        checkLater: "💡 Vuelve a revisar cuando los tiempos de enfriamiento expiren o las apelaciones sean revisadas."
  errors:
    errorTitle: "¡Error!"
    interactionCheck: "No puedes usar esta interacción, ya que no fuiste quien la invocó."
    rateLimited: "Estás siendo limitado por tasa de uso. Tómate un descanso."
    webhookRateLimit: "Has alcanzado el límite de creación de webhooks"
    invalidInput: "No has proporcionado una entrada válida."
    invalidInvite: "Esta invitación es inválida o ha expirado."
    webhookError: "Error al crear webhook."
    notConnected: "No pude encontrar un hub conectado en este canal."
    noInteraction: "Este comando sólo soporta comandos de barra."
    missingAppealReference: "Falta referencia de apelación."
    whoops: "¡Ups! Algo salió mal. Por favor intenta nuevamente más tarde."
    missingArgument: "Falta argumento: `{param}`."
    notConnectedServer: "{cross} No pude encontrar conexiones para este servidor."
  moderation:
    permissions:
      managerRequired: "Se requieren permisos de Manager+."
    target:
      both: "Por favor especifica un usuario o un servidor, no ambos."
      missing: "Por favor especifica un usuario o un servidor."
    revoke:
      noActive: "No se encontró {action} activo."
      success: "Revocado {action}."
    delete:
      notImplemented: "Eliminación de mensaje no implementada aún. Razón registrada: {reason}"
      noMessage: "No se proporcionó mensaje para eliminar."
      success: "Mensaje {messageId} ha sido eliminado de todos los hubs conectados."
      notInterChatMessage: "Este mensaje no es un mensaje de InterChat o ya fue eliminado."
      failed: "Error al eliminar el mensaje. Por favor intenta nuevamente más tarde."
      notFound: "Infracción no encontrada."
    blacklist:
      permissionDenied: "Careces de permisos de staff de InterChat para emitir una lista negra global."
      alreadyActive: "El objetivo ya tiene una lista negra global activa."
      success: "Lista negra global aplicada a {target}."
    success:
      action: '{target} ha sido {action} {prep} {hubName}'
    errors:
      selectedHubNotFound: "Hub seleccionado no encontrado."
      processingFailed: "Error al procesar acción de moderación: {error}"
      unknownAction: "Acción desconocida."
      unsupportedAction: "Ruta de acción no soportada."
      openPanelFailed: "Error abriendo panel de moderación: {error}"
      notModeratorForHub: "No eres moderador de este hub."
      alreadyState: "{targetType} ya está {state} en este hub."
      invalidHubData: "Datos de hub inválidos."
      originalMessageNotFound: "Mensaje original no encontrado en la base de datos."
      fetchAuthorOrServerFailed: "No se pudo obtener el autor del mensaje o el servidor."
      hubNotFoundForMessage: "Hub no encontrado para este mensaje."
      noModeratedHubs: "No tienes permisos de moderación en ningún hub."
      noTarget: "Por favor especifica un objetivo (usuario/servidor) o responde a un mensaje."
  infractions:
    errors:
      noPermission: "No tienes permiso para ver infracciones de este hub."
      bothSelection: "Por favor selecciona sólo un usuario o un servidor, no ambos."
      invalidServerId: "ID de servidor inválido."
    permissions:
      insufficient: "Necesitas permisos de {permission}+ en este hub."
      managerRequired: "Se requieren permisos de Manager+."
    target:
      both: "Por favor especifica un usuario o un servidor, no ambos."
      missing: "Por favor especifica un usuario o un servidor."
    success:
      action: "{action} {target} {prep} {hubName}."
    revoke:
      noActive: "No se encontró {action} activo."
      success: "Revocado {action}."
    delete:
      notImplemented: "Eliminación de mensaje no implementada aún. Razón registrada: {reason}"
      notFound: "Infracción no encontrada."
      success: "Infracción eliminada."
    blacklist:
      permissionDenied: "Careces de permisos de staff de InterChat para emitir una lista negra global."
      alreadyActive: "El objetivo ya tiene una lista negra global activa."
      success: "Lista negra global aplicada a {target}."
  report:
    errors:
      processingFailed: "Error procesando reporte: {error}"
      notFoundOrDeleted: "Reporte no encontrado o ha sido eliminado."
      alreadyHandled: "Este reporte ya está {status}."
      updateFailed: "Error al actualizar reporte. Por favor intenta nuevamente más tarde."
    success:
      actionPast: "Reporte {action}."
    dm:
      resolved: "Gracias por tu reporte. Nuestro equipo de moderación lo revisó y tomó la acción apropiada. No podemos compartir detalles específicos para proteger la privacidad de los usuarios."
  welcome:
    onGuildJoinTitle: "👋 ¡Hola!"
    onGuildJoinDescription: |
      **Soy InterChat, y me complace estar en tu servidor en nombre de nuestro equipo.**
      Juntos podemos conectar tu servidor con otras comunidades notables de Discord. Numerosos, potencialmente miles de servidores ubicados en un solo lugar, todos llenos de individuos ansiosos por conversar contigo. 🚀

      **¿Listo para comenzar a construir nuevos puentes con nosotros?**
      {dot} **¿Nuevo aquí?** El comando `/setup` proporciona una guía paso a paso para comenzar tu journey.
      {dot} **¿Listo para explorar?** Visita nuestra [página de descubrimiento](https://interchat.tech/hubs) para ubicar y unirte a nuestros hubs activos.
      {dot} **¿Tal vez algo más familiar?** Prueba nuestro `/call` para conexiones uno a uno.

      💝 **¿Perdido? ¿Necesitas ayuda?** Te damos la bienvenida a nuestra [comunidad de soporte](https://discord.gg/8DhUA4HNpD). Nuestro equipo y comunidad estarán encantados de ayudarte. Ofrecemos todo el soporte y asistencia que necesites, ¡no dudes en unirte!
  hubEvents:
    serverJoined:
      title: "🎉 ¡Nuevo servidor se unió!"
      description: "**{serverName}** se ha unido al hub! Démosles una cálida bienvenida a nuestra comunidad."
      footer: "¡Bienvenido a {hubName}!"
  staff:
    blacklist:
      notFound: "No se encontró entrada de lista negra coincidente."
      removed: "Entrada de lista negra eliminada."
  user:
    achievements:
      placeholder: "Logros aquí"
