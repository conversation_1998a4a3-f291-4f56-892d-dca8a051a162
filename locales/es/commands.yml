commands:
  about:
    title: "Sobre InterChat"
    description_text: "InterChat conecta las comunidades de Discord a través de una discusión activa entre servidores. Los mensajes fluyen naturalmente entre servidores en tiempo real, ayudándote a construir comunidades comprometidas y centradas en temas."
    support_text: "¿Necesitas ayuda? ¡Únete a nuestro servidor de soporte para asistencia!"
    features:
      title: "Características"
      list: |
        - Conéctate con otros servidores para discusiones activas entre servidores
        - Mensajes que fluyen naturalmente entre servidores en tiempo real
        - Construye comunidades comprometidas y centradas en temas
        - Herramientas de moderación para mantener discusiones saludables
        - Panel visual para manejar tus centros, servidores y configuraciones
    buttons:
      vote: "Vota en top.gg"
      invite: "Invita a Interchat"
      dashboard: "Abre el panel"
      support: "Únete al soporte"
      shardInfo: "Información de fragmentos"
  setup:
    welcome:
      title: "Bienvenido a la configuración de InterChat"
      description: "Vamos a conectar tu servidor. Este mago te guiará a través de la creación o unión de un centro."
    options:
      create:
        label: "Crea un Centro"
        description: "Empieza tu propia comunidad de InterChat"
      join:
        label: "Únete a un Centro"
        description: "Conéctate a una comunidad existente"
    create:
      whatYoullCreate:
        title: "Lo Que Crearás:"
        description: "{dot} Un espacio de comunidad único para tus servidores\n{dot} Control total sobre reglas y moderación\n{dot} Configuraciones y características personalizadas\n{dot} Privado por defecto - invitaciones solamente"
      youllNeed:
        title: "Necesitarás:"
        description: "{arrow_right} Nombre creativo del Centro\n{arrow_right} Descripción breve\n{arrow_right} Descripción detallada\n{arrow_right} URL de la imagen del logotipo"
    join:
      title: "Únete a un Centro"
      description: "Use el directorio público o conéctate con un código de invitación."
      publicHubs:
        title: "Centros Públicos"
        description: "**Navega** [interchat.tech/hubs](https://interchat.tech/hubs)\n**O clic** el botón de abajo para abrir el directorio"
      privateHubs:
        title: "Privado o Invitación solamente"
        description: "**Pídele al dueño o manager del centro** un código de invitación\n**Ejecuta `/connect <código-invitación>`** en tu servidor para unirte"
      footer: "Puedes unirte a más centros en cualquier momento, y puedes conectarte a múltiples centros a la vez en diferentes canales."
    nextSteps:
      created:
        title: "¡Centro Creado Exitosamente!"
        description: |
          Tu centro **{hubName}** está listo! Esto es lo que puedes hacer a continuación:
          - Invita tu primer servidor usando `/invite`
          - Establece reglas con `/rules set`
          - Personaliza configuraciones en el [panel](https://interchat.tech/dashboard)
          - Conecta canales con `/connect <nombre-centro>`
        inviteLink:
          title: "Siguiente: Crea Enlaces de Invitación"
          description: "Use {hubInviteCommand} para generar códigos de invitación para **{hubName}** y compártelos con otros propietarios de servidores."
        shareHub:
          title: "Comparte Tu Centro"
          description: "{dot} Publica sobre tu nuevo centro en nuestro [Servidor de Soporte]({supportInvite})\n{dot} Comparte con amigos y comunidades\n{dot} Usa redes sociales para difundir la palabra"
        proTips:
          title: "{dot} Consejos Profesionales"
          description: "{dot} Visita el [Panel]({website}) para configuraciones avanzadas\n{dot} Usa {hubVisibilityCommand} para hacer tu centro público\n{dot} Únete a nuestro [Servidor de Soporte]({supportInvite}) para ayuda y actualizaciones"
        footer: "¿Necesitas ayuda? Únete a nuestro servidor de soporte."
  report:
    title: "Denuncia el Mensaje"
    footer: "Las denuncias ayudan a mantener InterChat seguro para todos"
    contextMenu: "Denuncia el Mensaje"
    description: "Denuncia a {user} al **Staff del Centro** por violaciones del centro o al **Staff de InterChat** por violaciones de la plataforma. El usuario no será notificado, pero los moderadores pueden ver quién ha enviado la denuncia."
    success:
      title: "Denuncia Enviada"
      toStaff: "{tick} Tu denuncia ha sido enviada al staff de InterChat para revisión."
      toHub: "{tick} Tu denuncia ha sido enviada a los moderadores de centros para revisión."
    errors:
      hubMessageOnly: "{x_icon} Solo puedes denunciar mensajes enviados a través de los centros de InterChat."
      cannotReportSelf: "{x_icon} No puedes denunciar tus propios mensajes."
  hubCreate:
    success:
      description: "Tu centro **{hubName}** está listo! Esto es lo que puedes hacer a continuación:"
    errors:
      hubCreationFailed: "Algo salió mal al crear tu centro. Por favor intenta nuevamente."
      troubleshooting: "Lo que puedes hacer:"
      troubleshootingSteps: "• Verifica que tu URL del logo sea válida\n• Intenta nuevamente en unos momentos\n• Contacta a soporte si esto persiste"
  general:
    invite:
      title: "¡Hola!"
      description: "Gracias por elegir InterChat. ¿Necesitas ayuda? Únete a nuestro servidor de soporte. Usa los botones de abajo para invitar a nuestro bot a tu servidor y comenzar a conectar servidores."
  stats:
    title: "Métricas de InterChat"
    shard:
      title: "Información de Fragmentos"
      statusReady: "Listo"
      statusProvisioning: "Aprovisionando..."
      current: "Fragmentos actuales #{id}"
  help:
    title: "Comandos de InterChat"
    description: "Explora nuestra amplia gama de comandos para ayudarte a ti y a tu comunidad a conectarse con otros."
    noDescription: "Sin descripción"
    name: "Ayuda"
  staff:
    blacklist:
      list:
        description: "Ver entradas de lista negra filtradas por usuarios o servidores"
    get:
      description: "Obtener información sobre entidades de InterChat"
      server:
        description: "Obtenga información detallada sobre un servidor"
      hub:
        description: "Obtenga información detallada sobre un centro"
      user:
        description: "Obtenga información detallada sobre un usuario"
  profile:
    achievements:
      noneFound: "Ningún Encontrado"
      noneFoundDescription: "¡No pude encontrar ningún logro para este usuario!"
    badges:
      noneFound: "Ningún Encontrado"
      noneFoundDescription: "¡No pude encontrar ninguna insignia para este usuario!"
  leaderboard:
    staffTag: "Staff de InterChat"
    userTag: "Usuario"
    messagesColumn: "Mensajes"
    voteCountColumn: "Recuento de Votos"
  my:
    hubs:
      title: "Tus Centros"
      description: "Aquí están los centros que usted posee o modera:"
      position: "Posición:"
      owner: "Dueño"
  appeal:
    title: "Tus Infracciones Apelables"
    description: "Selecciona una infracción a continuación para enviar una apelación, o ver el estado de apelaciones existentes."
    noInfractions:
      title: "Sin Infracciones Activas"
      description: "No tienes infracciones activas que puedan ser apeladas."
    notAppealable: "Esta infracción ya no es apelable."
  mod:
    panel:
      description: "Abrir el panel de moderación para usuarios, mensajes o servidores"
      contextMenu: "Panel de Mod"
    ban:
      description: "Banear a un usuario o servidor de un centro."
    mute:
      description: "Silenciar a un usuario o servidor de un centro."
    warn:
      description: "Advertir a un usuario o servidor de un centro."
    unmute:
      description: "Revocar infracciones de silencio activas para un usuario o servidor en un centro."
    unban:
      description: "Revocar infracciones de ban activas para un usuario o servidor en un centro."
    delete:
      description: "Eliminar un mensaje de InterChat usando un enlace de mensaje de Discord."
    delete_infraction:
      description: "Eliminar una infracción (Solo para Managers+)."
  infractions:
    description: "Ver infracciones en un centro, filtradas por usuario o servidor."
  rules:
    description: "Ver las reglas de un centro"
    title: "Reglas {hubName}"
    noRules:
      title: "No Hay Reglas Establecidas"
      description: "Aún no se han establecido reglas para este centro."
    footer: "{count} regla{plural} • Sigue estas pautas para mantener una comunidad positiva"
    errors:
      noHub: "Este comando debe usarse en un canal de servidor conectado a un centro, o debes especificar un nombre de centro."
      notConnected: "Este canal no está conectado a ningún centro. Por favor especifica un nombre de centro o usa este comando en un canal conectado."
      hubNotFound: "Centro \"{hubName}\" no encontrado."
  connections:
    title: "Gestión de Conexiones"
    description: "Configura y administra todas las conexiones de centros dentro de este servidor."
    fields:
      lastActive: "Última Actividad"
    selected:
      description: "Ahora puedes editar esta conexión. Usa los menús desplegables a continuación para cambiar el canal de transmisión o editar configuraciones relacionadas con la conexión."
      fields:
        broadcastChannel: 'Transmitiendo a'
        connectionState: 'Estado de transmisión'
        lastActive: 'Última Actividad'
      state:
        enabled: 'Las transmisiones ahora están **activas**.'
        disabled: 'Las transmisiones ahora están **pausadas**.'
    fix:
      title: 'Validación de Conexión'
      description: 'Todas las conexiones dentro de este gremio han sido validadas. Mira abajo para más información.'
      responses:
        success:
          fixed: '¡Conexión reparada!'
          valid: 'No se encontraron problemas.'
        errors:
          channelDeleted: 'El canal asociado con este centro ha sido eliminado. Conexión removida.'
          permissionsWebhook: 'No puedo gestionar webhooks, por favor revisa mis permisos e intenta nuevamente.'
          permissionsView: 'No puedo ver el canal, por favor revisa mis permisos e intenta nuevamente.'
          permissionsSend: 'No puedo enviar mensajes, por favor revisa mis permisos e intenta nuevamente.'

