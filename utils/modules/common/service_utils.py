from __future__ import annotations
import asyncio
import random
from functools import wraps
from typing import Any, Awaitable, Callable, Dict, List, Optional, ParamSpec, TypeVar

from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
import aiohttp
import sqlalchemy.exc as sa_exc

from utils.constants import logger


class DatabaseOperationHelper:
    @staticmethod
    async def get_or_create(
        session: 'AsyncSession',
        model_class: type,
        defaults: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> tuple[Any, bool]:
        stmt = select(model_class).where(
            *[getattr(model_class, key) == value for key, value in kwargs.items()]
        )
        result = await session.execute(stmt)
        instance = result.scalar_one_or_none()

        if instance:
            return instance, False

        # Create new record
        create_kwargs = kwargs.copy()
        if defaults:
            create_kwargs.update(defaults)

        instance = model_class(**create_kwargs)
        session.add(instance)
        return instance, True

    @staticmethod
    async def bulk_get_by_ids(
        session: 'AsyncSession',
        model_class: type,
        ids: List[str],
        load_relationships: Optional[List[str]] = None,
    ) -> List[Any]:
        stmt = select(model_class).where(model_class.id.in_(ids))

        if load_relationships:
            for relationship in load_relationships:
                stmt = stmt.options(selectinload(getattr(model_class, relationship)))

        result = await session.execute(stmt)
        return list(result.scalars().all())

    @staticmethod
    async def update_by_id(
        session: 'AsyncSession', model_class: type, record_id: str, **update_values
    ) -> bool:
        try:
            stmt = update(model_class).where(model_class.id == record_id).values(**update_values)
            result = await session.execute(stmt)
            return result.rowcount > 0
        except Exception as e:
            logger.error(f'Failed to update {model_class.__name__} {record_id}: {e}')
            return False

    @staticmethod
    async def delete_by_id(session: 'AsyncSession', model_class: type, record_id: str) -> bool:
        try:
            stmt = delete(model_class).where(model_class.id == record_id)
            result = await session.execute(stmt)
            return result.rowcount > 0
        except Exception as e:
            logger.error(f'Failed to delete {model_class.__name__} {record_id}: {e}')
            return False


T = TypeVar('T')


class CacheOperationHelper:
    @staticmethod
    async def execute_with_cache_reload(
        operation_func: Callable[[], Awaitable[T]],
        cache_reload_func: Optional[Callable[[], Awaitable[None]]] = None,
        error_message: str = 'Operation with cache reload failed',
    ) -> Optional[T]:
        try:
            result = await operation_func()

            # Reload cache if function provided
            if cache_reload_func:
                await cache_reload_func()

            return result
        except Exception as e:
            logger.error(f'{error_message}: {e}', exc_info=True)
            return None


class ServiceQueryBuilder:
    @staticmethod
    def build_filter_query(
        model_class: type,
        filters: Dict[str, Any],
        order_by: Optional[str] = None,
        limit: Optional[int] = None,
    ):
        stmt = select(model_class)

        # Apply filters
        for field, value in filters.items():
            if hasattr(model_class, field):
                if isinstance(value, list):
                    stmt = stmt.where(getattr(model_class, field).in_(value))
                else:
                    stmt = stmt.where(getattr(model_class, field) == value)

        # Apply ordering
        if order_by and hasattr(model_class, order_by):
            stmt = stmt.order_by(getattr(model_class, order_by))

        # Apply limit
        if limit:
            stmt = stmt.limit(limit)

        return stmt


P = ParamSpec('P')
R = TypeVar('R')


def retry_with_backoff(
    retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 30.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
    exceptions: tuple[type[Exception], ...] = (
        asyncio.TimeoutError,
        aiohttp.ClientError,
        sa_exc.OperationalError,
    ),
):
    """Common util to retry any async function with exponential backoff."""

    def decorator(func: Callable[P, Awaitable[R]]) -> Callable[P, Awaitable[R]]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            for attempt in range(retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if not isinstance(e, exceptions):
                        raise

                    last_exception = e
                    if attempt == retries:
                        logger.warning(
                            f'Function {func.__name__} failed after {retries + 1} attempts. Final error: {e}'
                        )
                        break

                    delay = min(base_delay * (backoff_factor**attempt), max_delay)
                    if jitter:
                        jitter_range = delay * 0.2
                        delay += random.uniform(-jitter_range, jitter_range)

                    delay = max(0.1, delay)

                    logger.debug(
                        f'Function {func.__name__} failed with {type(e).__name__}. '
                        f'Retrying in {delay:.2f}s (attempt {attempt + 1}/{retries}).'
                    )
                    await asyncio.sleep(delay)

            if last_exception:
                raise last_exception

        return wrapper

    return decorator
