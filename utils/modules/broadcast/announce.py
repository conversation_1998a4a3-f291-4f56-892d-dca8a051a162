import discord
from sqlalchemy import select
from typing import TYPE_CHECKING
from utils.modules.core.db.models import Connection, Hub

from utils.constants import logger

if TYPE_CHECKING:
    from main import Bot


async def broadcast_announcement(bot: 'Bot', hub: Hub, embed: discord.Embed):
    async with bot.db.get_session() as session:
        stmt = select(Connection.webhookURL, Connection.parentId).where(Connection.hubId == hub.id)
        connections = (await session.execute(stmt)).all()

    for webhook_url, parent_id in connections:
        try:
            webhook = discord.Webhook.from_url(webhook_url, client=bot)

            if parent_id:
                await webhook.send(
                    embed=embed,
                    username=f'{hub.name} | Official Hub Announcement',
                    thread=discord.Object(id=parent_id)
                )
            else:
                try:
                    await webhook.send(
                        embed=embed,
                        username=f'{hub.name} | Official Hub Announcement'
                    )
                except discord.HTTPException as e:
                    if e.code == 220001:
                        await webhook.send(
                            embed=embed,
                            username=f'{hub.name} | Official Hub Announcement',
                            thread_name=f"Hub Announcement - {hub.name}"
                        )
                    else:
                        raise

        except discord.NotFound:
            logger.warning(f'Webhook {webhook_url} raised discord.NotFound - Skipping.')
            continue
        except ValueError:
            logger.warning('Webhook URL is invalid - Skipping.')
            continue
        except Exception as e:
            logger.warning(f'Failed to send announcement to webhook {webhook_url}: {e}')
            continue
