from typing import TYPE_CHECKING, Optional

import discord
from sqlalchemy import select

from utils.constants import logger
from utils.modules.core.db.models import Hub, HubLogConfig
from utils.modules.core.i18n import t
from utils.modules.ui.views.hubConfig.utils import BaseHubView
from utils.modules.hub.constants import HubPermissionLevel

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.ui.views.hubConfig.hubConfigurationView import ConfigurationView


class LoggingTypes:
    MODERATION_LOGS = t('ui.hubConfig.logging.moderationLogs', locale='en')
    JOIN_LEAVE_LOGS = t('ui.hubConfig.logging.joinLeaveLogs', locale='en')
    APPEALS_CHANNEL = t('ui.hubConfig.logging.appealsChannel', locale='en')
    REPORTS_CHANNEL = t('ui.hubConfig.logging.reportsChannel', locale='en')
    NETWORK_ALERTS = t('ui.hubConfig.logging.networkAlerts', locale='en')
    MESSAGE_MODERATION = t('ui.hubConfig.logging.messageModeration', locale='en')


class LoggingSettingsView(BaseHubView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        permission: HubPermissionLevel,
        locale: str,
        parent_view: Optional['ConfigurationView'] = None,
    ):
        super().__init__(bot, user, hub, permission, locale)
        self.parent_view = parent_view
        self.current_log_config: Optional[HubLogConfig] = None
        self.selected_logging_type: Optional[str] = None

        # Setup the logging configuration select
        self._setup_logging_select()
        self.add_back_button(self.parent_view)
        self.add_dashboard_button()

    def _setup_logging_select(self):
        options = [
            discord.SelectOption(
                label=LoggingTypes.MODERATION_LOGS,
                description='Channel for moderation actions (warns, bans, mutes)',
                value='mod_logs',
                emoji=self.bot.emotes.hammer_icon,
            ),
            discord.SelectOption(
                label=LoggingTypes.JOIN_LEAVE_LOGS,
                description='Channel for hub connection events',
                value='join_leaves',
                emoji=self.bot.emotes.join,
            ),
            discord.SelectOption(
                label=LoggingTypes.APPEALS_CHANNEL,
                description='Channel for appeal notifications',
                value='appeals',
                emoji=self.bot.emotes.scale_icon,
            ),
            discord.SelectOption(
                label=LoggingTypes.REPORTS_CHANNEL,
                description='Channel for user reports',
                value='reports',
                emoji=self.bot.emotes.megaphone_icon,
            ),
            discord.SelectOption(
                label=LoggingTypes.NETWORK_ALERTS,
                description='Channel for network-wide alerts and issues',
                value='network_alerts',
                emoji=self.bot.emotes.globe_icon,
            ),
            discord.SelectOption(
                label=LoggingTypes.MESSAGE_MODERATION,
                description='Channel for message edit/delete logs',
                value='message_moderation',
                emoji=self.bot.emotes.chat_icon,
            ),
        ]

        # Create the select menu
        self.logging_select_menu = discord.ui.Select(
            placeholder='Select logging type to configure...',
            options=options,
            min_values=1,
            max_values=1,
            row=0,
        )

        # Create a proper callback that wraps our method
        async def logging_select_callback(interaction):
            await self.logging_select(interaction, self.logging_select_menu)

        self.logging_select_menu.callback = logging_select_callback
        self.add_item(self.logging_select_menu)

    async def create_logging_embed(self) -> discord.Embed:
        async with self.bot.db.get_session() as session:
            stmt = select(HubLogConfig).where(HubLogConfig.hubId == self.hub.id)
            self.current_log_config = await session.scalar(stmt)

        embed = discord.Embed(
            color=self.constants.color,
        )

        embed.description = (
            f'### {self.bot.emotes.hash_icon} Hub Logging Configuration\n'
            '> *Configure logging channels and notification roles for different types of events.*'
        )

        # Create status display for each logging type
        logging_status = []

        config_map = {
            LoggingTypes.MODERATION_LOGS: (
                self.current_log_config.modLogsChannelId if self.current_log_config else None,
                self.current_log_config.modLogsRoleId if self.current_log_config else None,
            ),
            LoggingTypes.JOIN_LEAVE_LOGS: (
                self.current_log_config.joinLeavesChannelId if self.current_log_config else None,
                self.current_log_config.joinLeavesRoleId if self.current_log_config else None,
            ),
            LoggingTypes.APPEALS_CHANNEL: (
                self.current_log_config.appealsChannelId if self.current_log_config else None,
                self.current_log_config.appealsRoleId if self.current_log_config else None,
            ),
            LoggingTypes.REPORTS_CHANNEL: (
                self.current_log_config.reportsChannelId if self.current_log_config else None,
                self.current_log_config.reportsRoleId if self.current_log_config else None,
            ),
            LoggingTypes.NETWORK_ALERTS: (
                self.current_log_config.networkAlertsChannelId if self.current_log_config else None,
                self.current_log_config.networkAlertsRoleId if self.current_log_config else None,
            ),
            LoggingTypes.MESSAGE_MODERATION: (
                self.current_log_config.messageModerationChannelId
                if self.current_log_config
                else None,
                self.current_log_config.messageModerationRoleId
                if self.current_log_config
                else None,
            ),
        }

        for log_type, (channel_id, role_id) in config_map.items():
            status_icon = self.bot.emotes.tick if channel_id else self.bot.emotes.cross

            channel_text = f'<#{channel_id}>' if channel_id else 'Not configured'
            role_text = f'<@&{role_id}>' if role_id else 'No role'

            logging_status.append(
                f'{status_icon} **{log_type}**\n-# Channel: {channel_text}\n-# Role: {role_text}'
            )

        # Split into two columns for better display
        mid_point = len(logging_status) // 2
        if logging_status:
            embed.add_field(
                name='\u200b',
                value='\n\n'.join(logging_status[:mid_point]),
                inline=True,
            )
            if len(logging_status) > mid_point:
                embed.add_field(
                    name='\u200b',
                    value='\n\n'.join(logging_status[mid_point:]),
                    inline=True,
                )

        # Add footer
        embed.set_footer(
            text=f'Hub: {self.hub.name} • Select a logging type above to configure',
            icon_url=self.hub.iconUrl
            or (self.bot.user.display_avatar.url if self.bot.user else None),
        )

        return embed

    async def logging_select(
        self, interaction: discord.Interaction['Bot'], select: discord.ui.Select
    ):
        """Handle logging type selection - show channel and role select menus."""
        if not await self.interaction_check(interaction):
            return

        await interaction.response.defer()

        selected_type = select.values[0]
        self.selected_logging_type = selected_type

        # Clear existing select menus and add new ones for configuration
        self.clear_items()

        # Re-add the logging type selector
        self._setup_logging_select()

        # Add channel select menu
        channel_select = discord.ui.ChannelSelect(
            placeholder='Select a channel for logging...',
            channel_types=[
                discord.ChannelType.text,
                discord.ChannelType.news,
                discord.ChannelType.public_thread,
                discord.ChannelType.private_thread,
            ],
            min_values=0,
            max_values=1,
            row=1,
        )
        channel_select.callback = self.channel_selected
        self.add_item(channel_select)

        # Add role select menu
        role_select = discord.ui.RoleSelect(
            placeholder='Select a role for notifications (optional)...',
            min_values=0,
            max_values=1,
            row=2,
        )
        role_select.callback = self.role_selected
        self.add_item(role_select)

        # Add apply button
        apply_button = discord.ui.Button(
            label='Apply Configuration',
            style=discord.ButtonStyle.green,
            disabled=True,
            row=3,
        )
        apply_button.callback = self.apply_logging_config
        self.add_item(apply_button)

        # Add clear button
        clear_button = discord.ui.Button(
            label='Clear Configuration',
            style=discord.ButtonStyle.red,
            row=3,
        )
        clear_button.callback = self.clear_logging_config
        self.add_item(clear_button)

        # Re-add back button
        self.add_back_button(self.parent_view, row=4)

        # Store selected values
        self.selected_channel_id = None
        self.selected_role_id = None

        # Update embed to show selected logging type
        embed = await self.create_logging_embed()

        # Map logging types to display names
        type_names = {
            'mod_logs': LoggingTypes.MODERATION_LOGS,
            'join_leaves': LoggingTypes.JOIN_LEAVE_LOGS,
            'appeals': LoggingTypes.APPEALS_CHANNEL,
            'reports': LoggingTypes.REPORTS_CHANNEL,
            'network_alerts': LoggingTypes.NETWORK_ALERTS,
            'message_moderation': LoggingTypes.MESSAGE_MODERATION,
        }

        embed.description = (
            f'### {self.bot.emotes.hash_icon} Configure {type_names.get(selected_type, selected_type)}\n'
            '> *Select a channel and optionally a role for notifications below.*'
        )

        await interaction.edit_original_response(embed=embed, view=self)

    async def channel_selected(self, interaction: discord.Interaction['Bot']):
        if not await self.interaction_check(interaction):
            return

        # Find the channel select component in the view
        channel_select = None
        for item in self.children:
            if isinstance(item, discord.ui.ChannelSelect):
                channel_select = item
                break

        if channel_select and channel_select.values:
            self.selected_channel_id = str(channel_select.values[0].id)
        else:
            self.selected_channel_id = None

        # Enable apply button if we have a selection
        for item in self.children:
            if isinstance(item, discord.ui.Button) and item.label == 'Apply Configuration':
                item.disabled = False
                break

        await interaction.response.edit_message(view=self)

    async def role_selected(self, interaction: discord.Interaction['Bot']):
        if not await self.interaction_check(interaction):
            return

        role_select = None
        for item in self.children:
            if isinstance(item, discord.ui.RoleSelect):
                role_select = item
                break

        if role_select and role_select.values:
            self.selected_role_id = str(role_select.values[0].id)
        else:
            self.selected_role_id = None

        await interaction.response.edit_message(view=self)

    async def apply_logging_config(self, interaction: discord.Interaction['Bot']):
        if not await self.interaction_check(interaction):
            return

        await interaction.response.defer()

        if not hasattr(self, 'selected_logging_type'):
            await interaction.followup.send(
                f'{self.bot.emotes.x_icon} Please select a logging type first.', ephemeral=True
            )
            return

        success = await self._update_log_config(self.selected_channel_id, self.selected_role_id)

        if success:
            type_names = {
                'mod_logs': LoggingTypes.MODERATION_LOGS,
                'join_leaves': LoggingTypes.JOIN_LEAVE_LOGS,
                'appeals': LoggingTypes.APPEALS_CHANNEL,
                'reports': LoggingTypes.REPORTS_CHANNEL,
                'network_alerts': LoggingTypes.NETWORK_ALERTS,
                'message_moderation': LoggingTypes.MESSAGE_MODERATION,
            }
            success_message = f'{self.bot.emotes.tick} {type_names.get(self.selected_logging_type or "") or "Unknown"} configuration updated!'

            await self._reset_view_to_original()

            updated_embed = await self.create_logging_embed()
            await interaction.edit_original_response(embed=updated_embed, view=self)

            await interaction.followup.send(success_message, ephemeral=True)
        else:
            await interaction.followup.send(
                f'{self.bot.emotes.x_icon} Failed to update logging configuration.', ephemeral=True
            )

    async def clear_logging_config(self, interaction: discord.Interaction['Bot']):
        if not await self.interaction_check(interaction):
            return

        await interaction.response.defer()

        if not hasattr(self, 'selected_logging_type'):
            await interaction.followup.send(
                f'{self.bot.emotes.x_icon} Please select a logging type first.', ephemeral=True
            )
            return

        # Clear the configuration (set to None)
        success = await self._update_log_config(None, None)

        if success:
            # Reset view to original state
            await self._reset_view_to_original()

            # Refresh the embed
            updated_embed = await self.create_logging_embed()
            await interaction.edit_original_response(embed=updated_embed, view=self)

            # Show success message
            type_names = {
                'mod_logs': LoggingTypes.MODERATION_LOGS,
                'join_leaves': LoggingTypes.JOIN_LEAVE_LOGS,
                'appeals': LoggingTypes.APPEALS_CHANNEL,
                'reports': LoggingTypes.REPORTS_CHANNEL,
                'network_alerts': LoggingTypes.NETWORK_ALERTS,
                'message_moderation': LoggingTypes.MESSAGE_MODERATION,
            }

            await interaction.followup.send(
                f'{self.bot.emotes.tick} {type_names.get(self.selected_logging_type or "") or "Unknown"} configuration cleared!',
                ephemeral=True,
            )
        else:
            await interaction.followup.send(
                f'{self.bot.emotes.x_icon} Failed to clear logging configuration.', ephemeral=True
            )

    async def _reset_view_to_original(self):
        self.clear_items()
        self._setup_logging_select()
        self.add_back_button(self.parent_view)
        self.add_dashboard_button()

        # Clear stored values
        if hasattr(self, 'selected_logging_type'):
            delattr(self, 'selected_logging_type')
        if hasattr(self, 'selected_channel_id'):
            delattr(self, 'selected_channel_id')
        if hasattr(self, 'selected_role_id'):
            delattr(self, 'selected_role_id')

    async def _update_log_config(self, channel_id: Optional[str], role_id: Optional[str]) -> bool:
        """Update the log config in the database."""
        try:
            async with self.bot.db.get_session() as session:
                # Get existing log config
                stmt = select(HubLogConfig).where(HubLogConfig.hubId == self.hub.id)
                log_config = await session.scalar(stmt)

                # Create new if doesn't exist
                if not log_config:
                    log_config = HubLogConfig(hubId=self.hub.id)
                    session.add(log_config)

                # Mapping of logging types to their corresponding database fields
                attr_map = {
                    'mod_logs': ('modLogsChannelId', 'modLogsRoleId'),
                    'join_leaves': ('joinLeavesChannelId', 'joinLeavesRoleId'),
                    'appeals': ('appealsChannelId', 'appealsRoleId'),
                    'reports': ('reportsChannelId', 'reportsRoleId'),
                    'network_alerts': ('networkAlertsChannelId', 'networkAlertsRoleId'),
                    'message_moderation': ('messageModerationChannelId', 'messageModerationRoleId'),
                }

                # Update the appropriate fields based on selected logging type
                if self.selected_logging_type in attr_map:
                    channel_attr, role_attr = attr_map[self.selected_logging_type]
                    setattr(log_config, channel_attr, channel_id)
                    setattr(log_config, role_attr, role_id)
                else:
                    logger.warning(f'Unknown logging type: {self.selected_logging_type}')
                    return False

                # Commit the changes
                await session.commit()
                return True

        except Exception as e:
            logger.error(f'Error updating log config: {e}')
            return False
