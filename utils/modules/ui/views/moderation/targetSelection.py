import discord
from discord.ui import button, But<PERSON>
from typing import TYPE_CHECKING

from .utils import BaseModerationView, ModerationContext

if TYPE_CHECKING:
    from main import Bot


class TargetSelectionView(BaseModerationView):
    """View for selecting between user and server targets."""

    def __init__(
        self,
        context: ModerationContext,
        selected_action: str,
    ):
        super().__init__(context)
        self.selected_action = selected_action

    @button(label='Act on User', style=discord.ButtonStyle.primary, emoji='👤')
    async def select_user(self, interaction: discord.Interaction['Bot'], button: Button):  # noqa: ARG002
        """Select user as the target."""
        try:
            if not await self.validate_interaction(interaction):
                return

            # Import here to avoid circular imports
            from .modPanel import ReasonModal

            modal = ReasonModal(self.context, self.selected_action, target_type='user')
            await interaction.response.send_modal(modal)

        except Exception as e:
            await self.handle_error(interaction, e, 'Failed to open reason modal for user target.')

    @button(label='Act on Server', style=discord.ButtonStyle.secondary, emoji='🏢')
    async def select_server(self, interaction: discord.Interaction['Bot'], button: Button):  # noqa: ARG002
        """Select server as the target."""
        try:
            if not await self.validate_interaction(interaction):
                return

            from .modPanel import ReasonModal

            modal = ReasonModal(self.context, self.selected_action, target_type='server')
            await interaction.response.send_modal(modal)

        except Exception as e:
            await self.handle_error(
                interaction, e, 'Failed to open reason modal for server target.'
            )
