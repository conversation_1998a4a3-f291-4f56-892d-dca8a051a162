from typing import TYPE_CHECKING, Optional

import discord
from discord.ui import Modal, Select, TextInput

from utils.constants import logger
from utils.modules.core.checks import is_interchat_staff_direct
from utils.modules.core.db.models import Hub
from utils.modules.core.i18n import t
from utils.modules.services.moderation.actionHandler import ModerationActionHandler
from utils.modules.services.moderation.types import ActionType

from .utils import (
    DEFAULT_VIEW_TIMEOUT,
    MAX_DURATION_LENGTH,
    MAX_REASON_LENGTH,
    MIN_DURATION_LENGTH,
    MIN_REASON_LENGTH,
    BaseModerationView,
    ModerationContext,
    ModerationValidator,
    ValidationError,
)

if TYPE_CHECKING:
    from main import Bot


class ModPanelView(BaseModerationView):
    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
        selected_hub: Hub,
        locale: str,
    ):
        context = ModerationContext(
            bot=bot,
            moderator=moderator,
            target_user=target_user,
            target_server=target_server,
            target_message=target_message,
            selected_hub=selected_hub,
            locale=locale,
        )

        super().__init__(context)
        self.add_item(ModActionDropdown(self))

    async def handle_action_selection(self, interaction: discord.Interaction['Bot'], action: str):
        try:
            # Special case: message deletion should happen immediately
            if action == 'delete' and self.context.target_message:
                await self._handle_delete_action(interaction)
                return

            if self.context.has_both_targets:
                await self._show_target_selection_view(interaction, action)
            else:
                await self._open_reason_modal(interaction, action)

        except Exception as e:
            await self.handle_error(interaction, e, 'Failed to process action selection.')

    async def _handle_delete_action(self, interaction: discord.Interaction['Bot']):
        # Type guard: selected_hub should not be None when this method is called
        assert self.context.selected_hub is not None

        handler = ModerationActionHandler(
            self.bot,
            self.moderator,
            self.context.selected_hub,
            self.locale,
        )
        await handler.handle_delete_message(interaction, self.context.target_message)

    async def _show_target_selection_view(
        self, interaction: discord.Interaction['Bot'], action: str
    ):
        # Type guards: At this point we know both are not None due to has_both_targets check
        assert self.context.target_user is not None
        assert self.context.target_server is not None

        from .targetSelection import TargetSelectionView

        view = TargetSelectionView(
            self.context,
            action,
        )

        embed = discord.Embed(
            title=t('ui.moderation.targetSelection.title', locale=self.locale),
            description=t(
                'ui.moderation.targetSelection.description', locale=self.locale, action=action
            ),
            color=discord.Color.blue(),
        )
        embed.add_field(
            name=t('ui.moderation.targetSelection.userField', locale=self.locale),
            value=f'{self.context.target_user.mention} (`{self.context.target_user.id}`)',
            inline=True,
        )
        embed.add_field(
            name=t('ui.moderation.targetSelection.serverField', locale=self.locale),
            value=f'**{self.context.target_server.name}** (`{self.context.target_server.id}`)',
            inline=True,
        )

        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    async def _open_reason_modal(
        self,
        interaction: discord.Interaction['Bot'],
        action: str,
        target_type: Optional[str] = None,
    ):
        try:
            modal = ReasonModal(self.context, action, target_type)
            await interaction.response.send_modal(modal)
        except Exception as e:
            await self.handle_error(interaction, e, 'Failed to open reason modal.')


class ModActionDropdown(Select):
    def __init__(self, parent_view: ModPanelView):
        self.parent_view = parent_view
        self.context = parent_view.context
        self.locale = parent_view.locale

        try:
            options = self._build_action_options()
        except Exception:
            options = [
                discord.SelectOption(
                    label='Error loading actions', description='Please try again', value='error'
                )
            ]

        super().__init__(
            placeholder=t('ui.moderation.actionSelect.placeholder', locale=self.locale),
            options=options,
            min_values=1,
            max_values=1,
        )

    def _build_action_options(self) -> list[discord.SelectOption]:
        options = []
        emotes = self.parent_view.bot.emotes

        # Add delete option if message is provided
        if self.context.target_message:
            options.append(
                discord.SelectOption(
                    emoji=emotes.delete_icon,
                    label=t('ui.moderation.actions.delete.label', locale=self.locale),
                    description=t('ui.moderation.actions.delete.description', locale=self.locale),
                    value='delete',
                )
            )

        options.extend(
            [
                discord.SelectOption(
                    emoji=emotes.alert_icon,
                    label=t('ui.moderation.actions.warn.label', locale=self.locale),
                    description=t('ui.moderation.actions.warn.description', locale=self.locale),
                    value='warn',
                ),
                discord.SelectOption(
                    emoji=emotes.clock_icon,
                    label=t('ui.moderation.actions.mute.label', locale=self.locale),
                    description=t('ui.moderation.actions.mute.description', locale=self.locale),
                    value='mute',
                ),
                discord.SelectOption(
                    emoji=emotes.hammer_icon,
                    label=t('ui.moderation.actions.ban.label', locale=self.locale),
                    description=t('ui.moderation.actions.ban.description', locale=self.locale),
                    value='ban',
                ),
                # Revoke actions
                discord.SelectOption(
                    emoji=getattr(emotes, 'unmute_icon', None) or '🔊',
                    label=t('ui.moderation.actions.unmute.label', locale=self.locale),
                    description=t('ui.moderation.actions.unmute.description', locale=self.locale),
                    value='unmute',
                ),
                discord.SelectOption(
                    emoji=getattr(emotes, 'unban_icon', None) or '♻️',
                    label=t('ui.moderation.actions.unban.label', locale=self.locale),
                    description=t('ui.moderation.actions.unban.description', locale=self.locale),
                    value='unban',
                ),
            ]
        )

        # Add blacklist option for staff
        if is_interchat_staff_direct(self.parent_view.bot, self.parent_view.moderator.id):
            options.append(
                discord.SelectOption(
                    emoji=emotes.hammer_icon,
                    label=t('ui.moderation.actions.blacklist.label', locale=self.locale),
                    description=t(
                        'ui.moderation.actions.blacklist.description',
                        locale=self.locale,
                    ),
                    value='blacklist',
                )
            )

        return options

    async def callback(self, interaction: discord.Interaction['Bot']):  # pyright: ignore[reportIncompatibleMethodOverride]
        try:
            if not await self.parent_view.validate_interaction(interaction):
                return

            action = self.values[0]

            if action == 'error':
                await self.parent_view.send_error(
                    interaction, 'Please refresh the panel and try again.'
                )
                return

            await self.parent_view.handle_action_selection(interaction, action)

        except Exception as e:
            await self.parent_view.handle_error(
                interaction, e, 'Failed to process action selection.'
            )


class ReasonModal(Modal):
    def __init__(self, context: ModerationContext, action: str, target_type: Optional[str]):
        super().__init__(
            title=t('ui.moderation.modal.title', context.locale), timeout=DEFAULT_VIEW_TIMEOUT
        )
        self.context = context
        self.action = action
        self.target_type = target_type
        self.locale = context.locale

        self._setup_reason_input()
        self._setup_duration_input()

    def _setup_reason_input(self):
        reason_required = self.action not in {'unmute', 'unban'}
        optional_suffix = '' if reason_required else ' (optional)'
        placeholder = t(
            'ui.moderation.modal.reason.placeholder',
            locale=self.locale,
            optional=optional_suffix,
        )

        self.reason_input: TextInput = TextInput(
            label=t('ui.moderation.modal.reason.label', locale=self.locale),
            placeholder=placeholder,
            style=discord.TextStyle.paragraph,
            min_length=0 if not reason_required else MIN_REASON_LENGTH,
            max_length=MAX_REASON_LENGTH,
            required=reason_required,
        )
        self.add_item(self.reason_input)

    def _setup_duration_input(self):
        if self.action == 'mute':
            self.duration_input: TextInput = TextInput(
                label=t('ui.moderation.modal.duration.label', locale=self.locale),
                placeholder=t('ui.moderation.modal.duration.placeholder', locale=self.locale),
                style=discord.TextStyle.short,
                required=True,
                min_length=MIN_DURATION_LENGTH,
                max_length=MAX_DURATION_LENGTH,
            )
            self.add_item(self.duration_input)
        else:
            self.duration_input = None  # pyright: ignore[reportAttributeAccessIssue]

    async def on_submit(self, interaction: discord.Interaction['Bot']):  # pyright: ignore[reportIncompatibleMethodOverride]
        """Handle form submission."""
        try:
            # Parse and validate inputs
            reason = self.reason_input.value.strip()
            action_enum = ActionType(self.action)

            # Validate reason
            ModerationValidator.validate_reason(reason, action_enum)

            # Parse and validate duration if needed
            duration_ms = None
            if self.action == 'mute' and self.duration_input:
                duration_ms = ModerationValidator.validate_duration(
                    self.duration_input.value.strip(), action_enum
                )

            await self._execute_moderation_action(interaction, reason, duration_ms, action_enum)

        except ValidationError as e:
            await self._send_validation_error(interaction, str(e))
        except ValueError:
            await self._send_validation_error(interaction, 'Invalid action type.')
        except Exception as e:
            await self._send_generic_error(interaction, e)

    async def _send_validation_error(self, interaction: discord.Interaction, message: str):
        # TODO: Localize
        embed = discord.Embed(
            title='Validation Error',
            description=f'{self.context.bot.emotes.x_icon} {message}',
            color=discord.Color.red(),
        )
        if not interaction.response.is_done():
            await interaction.response.send_message(embed=embed, ephemeral=True)
        else:
            await interaction.followup.send(embed=embed, ephemeral=True)

    async def _send_generic_error(self, interaction: discord.Interaction, error: Exception):  # noqa: ARG002
        # TODO: Localize
        embed = discord.Embed(
            title='Error',
            description=f'{self.context.bot.emotes.x_icon} An error occurred while processing your request.',
            color=discord.Color.red(),
        )
        if not interaction.response.is_done():
            await interaction.response.send_message(embed=embed, ephemeral=True)
        else:
            await interaction.followup.send(embed=embed, ephemeral=True)

    async def _execute_moderation_action(
        self,
        interaction: discord.Interaction['Bot'],
        reason: str,
        duration_ms: Optional[int],
        action_enum: ActionType,
    ):
        target = self.context.create_target(self.target_type)
        ModerationValidator.validate_action_target_compatibility(action_enum, target)

        # Type guard: selected_hub should not be None when this method is called
        assert self.context.selected_hub is not None

        handler = ModerationActionHandler(
            self.context.bot,
            self.context.moderator,
            self.context.selected_hub,
            self.locale,
        )

        # Route to appropriate handler based on action type
        if action_enum == ActionType.DELETE and self.context.target_message:
            await handler.handle_delete_message(
                interaction, self.context.target_message, reason=reason or None
            )
        elif action_enum in {ActionType.WARN, ActionType.MUTE, ActionType.BAN}:
            await handler.handle_punitive_action(
                interaction, action_enum, target, reason or None, duration_ms=duration_ms
            )
        elif action_enum in {ActionType.UNMUTE, ActionType.UNBAN}:
            await handler.handle_revoke_action(interaction, action_enum, target)
        elif action_enum == ActionType.BLACKLIST:
            await handler.handle_global_blacklist_action(
                interaction, target, reason or None, duration_ms=duration_ms
            )
        else:
            await self._send_validation_error(interaction, 'Unsupported action type.')

    async def on_error(self, interaction: discord.Interaction, error: Exception) -> None:
        logger.error(f'Error in reason modal: {error}', exc_info=error)
        await self._send_generic_error(interaction, error)
