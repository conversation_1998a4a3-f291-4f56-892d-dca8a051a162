from collections.abc import Sequence
from typing import TYPE_CHECKING, Optional

import discord
from discord.ui import Select

from utils.modules.core.db.models import Hub
from utils.modules.core.i18n import t
from utils.modules.services.moderation.actionHandler import ModerationActionHandler
from utils.modules.services.moderation.types import ActionType, ModerationTarget
from utils.utils import parse_duration

from .utils import MAX_DESCRIPTION_LENGTH, MAX_SELECT_OPTIONS, BaseModerationView, ModerationContext

if TYPE_CHECKING:
    from main import Bot


class ActionHubSelectionView(BaseModerationView):
    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        user: Optional[discord.User | discord.Member],
        server: Optional[discord.Guild],
        user_hubs: Sequence[Hub],
        action: str,
        args: str,
        locale: str,
    ):
        context = ModerationContext(
            bot=bot,
            moderator=moderator,
            target_user=user,
            target_server=server,
            target_message=None,
            selected_hub=None,  # Will be set when hub is selected
            locale=locale,
        )
        super().__init__(context)
        self.user = user
        self.server = server
        self.user_hubs = list(user_hubs)
        self.action = action
        self.args = args

        options: list[discord.SelectOption] = []
        for hub in self.user_hubs[:MAX_SELECT_OPTIONS]:
            description = hub.shortDescription or t('ui.common.noDescription', locale=locale)
            if len(description) > MAX_DESCRIPTION_LENGTH:
                description = description[:MAX_DESCRIPTION_LENGTH]
            options.append(
                discord.SelectOption(
                    label=hub.name,
                    value=hub.id,
                    description=description,
                )
            )

        select = Select(
            placeholder=t('ui.moderation.hubSelection.fieldHubPrompt', locale=locale),
            options=options,
            min_values=1,
            max_values=1,
        )
        select.callback = self._on_hub_selected
        self.add_item(select)

    def _parse_args_for_target_and_reason(
        self, args: str, action: str
    ) -> tuple[Optional[str], Optional[int], str]:
        """Parse arguments to extract duration (for mute) and reason."""
        if not args:
            return None, None, ''
        parts = args.split()
        duration_ms = None
        i = 0
        if action == 'mute' and i < len(parts):
            try:
                duration_ms = parse_duration(parts[i])
                i += 1
            except ValueError:
                pass
        reason = ' '.join(parts[i:]) if i < len(parts) else None
        return reason, duration_ms, args

    async def _on_hub_selected(self, interaction: discord.Interaction['Bot']):
        if not await self.validate_interaction(interaction):
            return

        selected_hub_id = None
        if isinstance(self.children[0], Select) and self.children[0].values:
            selected_hub_id = self.children[0].values[0]

        selected_hub = next((h for h in self.user_hubs if h.id == selected_hub_id), None)
        if not selected_hub:
            await self.send_error(
                interaction, t('responses.moderation.errors.invalidHubData', self.locale)
            )
            return

        # Build target
        target = ModerationTarget(user=self.user, server=self.server)

        # Parse args
        reason, duration_ms, _ = self._parse_args_for_target_and_reason(self.args, self.action)

        # Validate duration for mute
        if self.action == 'mute' and duration_ms is None:
            await self.send_error(
                interaction,
                'Duration is required for mute actions. Use format like "1d", "2h", "30m".',
            )
            return

        handler = ModerationActionHandler(self.bot, self.moderator, selected_hub, self.locale)

        # Dispatch
        try:
            action_enum = ActionType(self.action)
        except ValueError:
            await self.send_error(
                interaction, t('responses.moderation.errors.unknownAction', self.locale)
            )
            return

        if action_enum in {ActionType.WARN, ActionType.MUTE, ActionType.BAN}:
            await handler.handle_punitive_action(
                interaction, action_enum, target, reason, duration_ms
            )
        elif action_enum in {ActionType.UNMUTE, ActionType.UNBAN}:
            await handler.handle_revoke_action(interaction, action_enum, target)
        else:
            await self.send_error(
                interaction, t('responses.moderation.errors.unsupportedAction', self.locale)
            )
