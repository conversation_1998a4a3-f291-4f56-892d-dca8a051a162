"""Auto-generated locale key stubs for type checking.

This file provides type hints for all available locale keys
that can be used with the t() function.
"""

from typing import Literal

# All available locale keys
LocaleKey = Literal[
    'commands.about.buttons.dashboard',
    'commands.about.buttons.invite',
    'commands.about.buttons.shardInfo',
    'commands.about.buttons.support',
    'commands.about.buttons.vote',
    'commands.about.description_text',
    'commands.about.features.list',
    'commands.about.features.title',
    'commands.about.support_text',
    'commands.about.title',
    'commands.appeal.description',
    'commands.appeal.noInfractions.description',
    'commands.appeal.noInfractions.title',
    'commands.appeal.notAppealable',
    'commands.appeal.title',
    'commands.connections.description',
    'commands.connections.fields.lastActive',
    'commands.connections.fix.description',
    'commands.connections.fix.responses.errors.channelDeleted',
    'commands.connections.fix.responses.errors.permissionsSend',
    'commands.connections.fix.responses.errors.permissionsView',
    'commands.connections.fix.responses.errors.permissionsWebhook',
    'commands.connections.fix.responses.success.fixed',
    'commands.connections.fix.responses.success.valid',
    'commands.connections.fix.title',
    'commands.connections.selected.description',
    'commands.connections.selected.fields.broadcastChannel',
    'commands.connections.selected.fields.connectionState',
    'commands.connections.selected.fields.lastActive',
    'commands.connections.selected.state.disabled',
    'commands.connections.selected.state.enabled',
    'commands.connections.title',
    'commands.general.invite.description',
    'commands.general.invite.title',
    'commands.help.description',
    'commands.help.name',
    'commands.help.noDescription',
    'commands.help.title',
    'commands.hubCreate.errors.hubCreationFailed',
    'commands.hubCreate.errors.troubleshooting',
    'commands.hubCreate.errors.troubleshootingSteps',
    'commands.hubCreate.success.description',
    'commands.infractions.description',
    'commands.leaderboard.messagesColumn',
    'commands.leaderboard.staffTag',
    'commands.leaderboard.userTag',
    'commands.leaderboard.voteCountColumn',
    'commands.mod.ban.description',
    'commands.mod.delete.description',
    'commands.mod.delete_infraction.description',
    'commands.mod.mute.description',
    'commands.mod.panel.contextMenu',
    'commands.mod.panel.description',
    'commands.mod.unban.description',
    'commands.mod.unmute.description',
    'commands.mod.warn.description',
    'commands.my.hubs.description',
    'commands.my.hubs.owner',
    'commands.my.hubs.position',
    'commands.my.hubs.title',
    'commands.profile.achievements.noneFound',
    'commands.profile.achievements.noneFoundDescription',
    'commands.profile.badges.noneFound',
    'commands.profile.badges.noneFoundDescription',
    'commands.report.contextMenu',
    'commands.report.description',
    'commands.report.errors.cannotReportSelf',
    'commands.report.errors.hubMessageOnly',
    'commands.report.footer',
    'commands.report.success.title',
    'commands.report.success.toHub',
    'commands.report.success.toStaff',
    'commands.report.title',
    'commands.rules.description',
    'commands.rules.errors.hubNotFound',
    'commands.rules.errors.noHub',
    'commands.rules.errors.notConnected',
    'commands.rules.footer',
    'commands.rules.noRules.description',
    'commands.rules.noRules.title',
    'commands.rules.title',
    'commands.setup.create.whatYoullCreate.description',
    'commands.setup.create.whatYoullCreate.title',
    'commands.setup.create.youllNeed.description',
    'commands.setup.create.youllNeed.title',
    'commands.setup.join.description',
    'commands.setup.join.footer',
    'commands.setup.join.privateHubs.description',
    'commands.setup.join.privateHubs.title',
    'commands.setup.join.publicHubs.description',
    'commands.setup.join.publicHubs.title',
    'commands.setup.join.title',
    'commands.setup.nextSteps.created.description',
    'commands.setup.nextSteps.created.footer',
    'commands.setup.nextSteps.created.inviteLink.description',
    'commands.setup.nextSteps.created.inviteLink.title',
    'commands.setup.nextSteps.created.proTips.description',
    'commands.setup.nextSteps.created.proTips.title',
    'commands.setup.nextSteps.created.shareHub.description',
    'commands.setup.nextSteps.created.shareHub.title',
    'commands.setup.nextSteps.created.title',
    'commands.setup.options.create.description',
    'commands.setup.options.create.label',
    'commands.setup.options.join.description',
    'commands.setup.options.join.label',
    'commands.setup.welcome.description',
    'commands.setup.welcome.title',
    'commands.staff.blacklist.list.description',
    'commands.staff.get.description',
    'commands.staff.get.hub.description',
    'commands.staff.get.server.description',
    'commands.staff.get.user.description',
    'commands.stats.shard.current',
    'commands.stats.shard.statusProvisioning',
    'commands.stats.shard.statusReady',
    'commands.stats.shard.title',
    'commands.stats.title',
    'responses.appeal.constants.noReason',
    'responses.appeal.constants.unknownHub',
    'responses.appeal.dm.accepted',
    'responses.appeal.dm.declined',
    'responses.appeal.dm.moderatorNote',
    'responses.appeal.embed.description',
    'responses.appeal.embed.footer.canAppeal',
    'responses.appeal.embed.footer.checkLater',
    'responses.appeal.embed.noInfractions.description',
    'responses.appeal.embed.noInfractions.title',
    'responses.appeal.embed.title',
    'responses.appeal.errors.notFoundOrDeleted',
    'responses.appeal.errors.recordFailed',
    'responses.appeal.errors.updateFailed',
    'responses.appeal.fields.date',
    'responses.appeal.fields.reason',
    'responses.appeal.status.canAppeal',
    'responses.appeal.status.canAppealAgain',
    'responses.appeal.status.cooldown',
    'responses.appeal.status.pending',
    'responses.common.hub',
    'responses.common.unknown',
    'responses.errors.errorTitle',
    'responses.errors.interactionCheck',
    'responses.errors.invalidInput',
    'responses.errors.invalidInvite',
    'responses.errors.missingAppealReference',
    'responses.errors.missingArgument',
    'responses.errors.noInteraction',
    'responses.errors.notConnected',
    'responses.errors.notConnectedServer',
    'responses.errors.rateLimited',
    'responses.errors.webhookError',
    'responses.errors.webhookRateLimit',
    'responses.errors.whoops',
    'responses.hubEvents.serverJoined.description',
    'responses.hubEvents.serverJoined.encouragement',
    'responses.hubEvents.serverJoined.footer',
    'responses.hubEvents.serverJoined.title',
    'responses.infractions.blacklist.alreadyActive',
    'responses.infractions.blacklist.permissionDenied',
    'responses.infractions.blacklist.success',
    'responses.infractions.delete.notFound',
    'responses.infractions.delete.notImplemented',
    'responses.infractions.delete.success',
    'responses.infractions.errors.bothSelection',
    'responses.infractions.errors.invalidServerId',
    'responses.infractions.errors.noPermission',
    'responses.infractions.permissions.insufficient',
    'responses.infractions.permissions.managerRequired',
    'responses.infractions.revoke.noActive',
    'responses.infractions.revoke.success',
    'responses.infractions.success.action',
    'responses.infractions.target.both',
    'responses.infractions.target.missing',
    'responses.moderation.blacklist.alreadyActive',
    'responses.moderation.blacklist.permissionDenied',
    'responses.moderation.blacklist.success',
    'responses.moderation.delete.failed',
    'responses.moderation.delete.noMessage',
    'responses.moderation.delete.notFound',
    'responses.moderation.delete.notImplemented',
    'responses.moderation.delete.notInterChatMessage',
    'responses.moderation.delete.success',
    'responses.moderation.errors.alreadyState',
    'responses.moderation.errors.fetchAuthorOrServerFailed',
    'responses.moderation.errors.hubNotFoundForMessage',
    'responses.moderation.errors.invalidHubData',
    'responses.moderation.errors.noModeratedHubs',
    'responses.moderation.errors.noTarget',
    'responses.moderation.errors.notModeratorForHub',
    'responses.moderation.errors.openPanelFailed',
    'responses.moderation.errors.originalMessageNotFound',
    'responses.moderation.errors.processingFailed',
    'responses.moderation.errors.selectedHubNotFound',
    'responses.moderation.errors.unknownAction',
    'responses.moderation.errors.unsupportedAction',
    'responses.moderation.permissions.managerRequired',
    'responses.moderation.revoke.noActive',
    'responses.moderation.revoke.success',
    'responses.moderation.success.action',
    'responses.moderation.target.both',
    'responses.moderation.target.missing',
    'responses.report.dm.resolved',
    'responses.report.errors.alreadyHandled',
    'responses.report.errors.notFoundOrDeleted',
    'responses.report.errors.processingFailed',
    'responses.report.errors.updateFailed',
    'responses.report.success.actionPast',
    'responses.setup.editMessagePrompt',
    'responses.setup.errors.hubCreationFailed',
    'responses.setup.loading.creatingHub',
    'responses.setup.loading.pleaseWait',
    'responses.setup.locale.successDescription',
    'responses.setup.locale.successTitle',
    'responses.setup.preview.description',
    'responses.setup.preview.name',
    'responses.setup.preview.previewTitle',
    'responses.setup.preview.short',
    'responses.setup.preview.titleSaved',
    'responses.setup.setupComplete',
    'responses.staff.blacklist.notFound',
    'responses.staff.blacklist.removed',
    'responses.user.achievements.placeholder',
    'responses.welcome.onGuildJoinDescription',
    'responses.welcome.onGuildJoinTitle',
    'ui.appeal.actions.decisionTitle',
    'ui.appeal.actions.reasonOptional.label',
    'ui.appeal.actions.reasonOptional.placeholder',
    'ui.appeal.buttons.next',
    'ui.appeal.buttons.previous',
    'ui.appeal.errors.cannotControl',
    'ui.appeal.errors.invalidSelection',
    'ui.appeal.errors.modalError',
    'ui.appeal.errors.notYourMenu',
    'ui.appeal.errors.nothingSelected',
    'ui.appeal.modal.q1',
    'ui.appeal.modal.q2',
    'ui.appeal.modal.q3',
    'ui.appeal.modal.title',
    'ui.appeal.select.placeholder',
    'ui.appeal.status.acceptedBy',
    'ui.appeal.status.rejectedBy',
    'ui.appeal.success.submitted',
    'ui.appeal.viewInfractions.empty',
    'ui.appeal.viewInfractions.title',
    'ui.common.create.footer.getStarted',
    'ui.common.errors.cannotControlPagination',
    'ui.common.errors.invalidFilter',
    'ui.common.errors.notFound',
    'ui.common.errors.notYourMenu',
    'ui.common.labels.cancel',
    'ui.common.labels.create',
    'ui.common.labels.date',
    'ui.common.messages.hubConfigDescription',
    'ui.common.messages.hubNotFound',
    'ui.common.messages.hubUpdateFailed',
    'ui.common.messages.interfacePermission',
    'ui.common.messages.loading',
    'ui.common.messages.notImplemented',
    'ui.common.messages.notSpecified',
    'ui.common.messages.pleaseWait',
    'ui.common.messages.serverNotFound',
    'ui.common.messages.userNotFound',
    'ui.common.modal.hubCreation.brief.label',
    'ui.common.modal.hubCreation.brief.placeholder',
    'ui.common.modal.hubCreation.description.label',
    'ui.common.modal.hubCreation.description.placeholder',
    'ui.common.modal.hubCreation.logoUrl.label',
    'ui.common.modal.hubCreation.logoUrl.placeholder',
    'ui.common.modal.hubCreation.name.label',
    'ui.common.modal.hubCreation.name.placeholder',
    'ui.common.modal.hubCreation.title',
    'ui.common.noDescription',
    'ui.common.pagination.next',
    'ui.common.pagination.page',
    'ui.common.pagination.previous',
    'ui.common.titles.cancelled',
    'ui.common.titles.error',
    'ui.common.titles.success',
    'ui.connection.fixAll.label',
    'ui.connection.manage.placeholder',
    'ui.help.select.placeholder',
    'ui.hub.all.description',
    'ui.hub.all.errors.invalidFilter',
    'ui.hub.all.errors.notFound',
    'ui.hub.all.filteredBy',
    'ui.hub.all.modal.searchFilter.label',
    'ui.hub.all.modal.searchFilter.placeholder',
    'ui.hub.all.modal.title',
    'ui.hub.all.title',
    'ui.hub.announcements.description',
    'ui.hub.announcements.title',
    'ui.hub.config.description',
    'ui.hub.config.options.announcements.description',
    'ui.hub.config.options.announcements.label',
    'ui.hub.config.options.general.description',
    'ui.hub.config.options.general.label',
    'ui.hub.config.options.logging.description',
    'ui.hub.config.options.logging.label',
    'ui.hub.config.options.modules.description',
    'ui.hub.config.options.modules.label',
    'ui.hub.config.options.rules.description',
    'ui.hub.config.options.rules.label',
    'ui.hub.config.options.team.description',
    'ui.hub.config.options.team.label',
    'ui.hub.config.options.transfer.description',
    'ui.hub.config.options.transfer.label',
    'ui.hub.config.title',
    'ui.hub.connect.errors.alreadyConnected',
    'ui.hub.connect.success.description',
    'ui.hub.connect.success.fieldValue',
    'ui.hub.connect.success.title',
    'ui.hub.creation.clickToBegin',
    'ui.hub.creation.complete.description',
    'ui.hub.creation.complete.private',
    'ui.hub.creation.complete.public',
    'ui.hub.creation.complete.title',
    'ui.hub.creation.description',
    'ui.hub.creation.errors.invalidInput',
    'ui.hub.creation.errors.name',
    'ui.hub.creation.errors.shortDescription',
    'ui.hub.creation.errors.unhandled',
    'ui.hub.creation.errors.unique',
    'ui.hub.creation.modal.hubName',
    'ui.hub.creation.modal.shortLabel',
    'ui.hub.creation.modal.title',
    'ui.hub.creation.other.cancelled',
    'ui.hub.creation.preview.description',
    'ui.hub.creation.preview.fields.title',
    'ui.hub.creation.preview.fields.value',
    'ui.hub.creation.preview.footer',
    'ui.hub.creation.preview.title',
    'ui.hub.creation.title',
    'ui.hub.creation.visibility.buttons.private',
    'ui.hub.creation.visibility.buttons.public',
    'ui.hub.creation.visibility.description',
    'ui.hub.creation.visibility.title',
    'ui.hub.delete.description',
    'ui.hub.description',
    'ui.hub.disconnect.description',
    'ui.hub.disconnect.fieldValue',
    'ui.hub.disconnect.title',
    'ui.hub.invites.inviteExpire',
    'ui.hub.invites.inviteUses',
    'ui.hub.invites.noneFound',
    'ui.hub.invites.title',
    'ui.hub.rules.addRule.label',
    'ui.hub.rules.addRule.modal.placeholder',
    'ui.hub.rules.addRule.modal.title',
    'ui.hub.rules.deleteRule.confirmation',
    'ui.hub.rules.deleteRule.label',
    'ui.hub.rules.description',
    'ui.hub.rules.display.currentRule',
    'ui.hub.rules.display.currentRulesTitle',
    'ui.hub.rules.display.noRulesDescription',
    'ui.hub.rules.display.noRulesTitle',
    'ui.hub.rules.display.ruleSelected',
    'ui.hub.rules.editRule.label',
    'ui.hub.rules.editRule.modal.placeholder',
    'ui.hub.rules.editRule.modal.title',
    'ui.hub.rules.errors.actionCancelled',
    'ui.hub.rules.errors.actionCancelledDescription',
    'ui.hub.rules.errors.deletionCancelled',
    'ui.hub.rules.errors.deletionCancelledDescription',
    'ui.hub.rules.errors.invalidRuleSelection',
    'ui.hub.rules.errors.maxRulesReached',
    'ui.hub.rules.errors.navigationError',
    'ui.hub.rules.errors.noRulesAvailable',
    'ui.hub.rules.errors.processingError',
    'ui.hub.rules.errors.refreshError',
    'ui.hub.rules.errors.ruleNotFound',
    'ui.hub.rules.errors.selectionError',
    'ui.hub.rules.errors.sessionExpired',
    'ui.hub.rules.errors.sessionExpiredDescription',
    'ui.hub.rules.moveDown.label',
    'ui.hub.rules.moveUp.label',
    'ui.hub.rules.noRules',
    'ui.hub.rules.selectRule.placeholder',
    'ui.hub.rules.success.added',
    'ui.hub.rules.success.deleted',
    'ui.hub.rules.success.moved',
    'ui.hub.rules.success.updated',
    'ui.hub.rules.title',
    'ui.hub.rules.validation.empty',
    'ui.hub.rules.validation.maxRules',
    'ui.hub.rules.validation.tooLong',
    'ui.hub.rules.viewAll.label',
    'ui.hub.rules.warnings.deleteWarning',
    'ui.hub.rules.warnings.deleteWarningDescription',
    'ui.hub.title',
    'ui.hubConfig.general.editDescription.description',
    'ui.hubConfig.general.editDescription.label',
    'ui.hubConfig.general.editName.description',
    'ui.hubConfig.general.editName.label',
    'ui.hubConfig.general.placeholder',
    'ui.hubConfig.general.toggleNsfw.description',
    'ui.hubConfig.general.toggleNsfw.label',
    'ui.hubConfig.general.togglePrivate.description',
    'ui.hubConfig.general.togglePrivate.label',
    'ui.hubConfig.general.welcomeMessage.description',
    'ui.hubConfig.general.welcomeMessage.label',
    'ui.hubConfig.invites.buttons.create',
    'ui.hubConfig.invites.create.customCode.label',
    'ui.hubConfig.invites.create.customCode.placeholder',
    'ui.hubConfig.invites.create.expiry.label',
    'ui.hubConfig.invites.create.expiry.placeholder',
    'ui.hubConfig.invites.create.title',
    'ui.hubConfig.invites.create.uses.label',
    'ui.hubConfig.invites.create.uses.placeholder',
    'ui.hubConfig.logging.appealsChannel',
    'ui.hubConfig.logging.joinLeaveLogs',
    'ui.hubConfig.logging.messageModeration',
    'ui.hubConfig.logging.moderationLogs',
    'ui.hubConfig.logging.networkAlerts',
    'ui.hubConfig.logging.reportsChannel',
    'ui.hubConfig.main.loadingDescription',
    'ui.hubConfig.main.loadingLabel',
    'ui.hubConfig.main.placeholder',
    'ui.hubConfig.modules.blockInvites.description',
    'ui.hubConfig.modules.hideLinks.description',
    'ui.hubConfig.modules.reactions.description',
    'ui.hubConfig.modules.spamFilter.description',
    'ui.hubConfig.modules.useNicknames.description',
    'ui.hubConfig.permissions.manager.description',
    'ui.hubConfig.permissions.manager.label',
    'ui.hubConfig.permissions.managerOnly.description',
    'ui.hubConfig.permissions.moderator.description',
    'ui.hubConfig.permissions.moderator.label',
    'ui.hubConfig.permissions.remove.label',
    'ui.infractions.buttons.serverInfractions',
    'ui.infractions.buttons.userInfractions',
    'ui.infractions.descriptions.base',
    'ui.infractions.descriptions.serverListEmpty',
    'ui.infractions.descriptions.serverSpecificEmpty',
    'ui.infractions.descriptions.userListEmpty',
    'ui.infractions.descriptions.userSpecificEmpty',
    'ui.infractions.fields.issued',
    'ui.infractions.fields.moderator',
    'ui.infractions.fields.reason',
    'ui.infractions.fields.serverId',
    'ui.infractions.fields.serverName',
    'ui.infractions.fields.status',
    'ui.infractions.fields.userId',
    'ui.infractions.fields.userName',
    'ui.infractions.labels.infraction',
    'ui.infractions.titles.base',
    'ui.infractions.titles.serverList',
    'ui.infractions.titles.serverSpecific',
    'ui.infractions.titles.userList',
    'ui.infractions.titles.userSpecific',
    'ui.moderation.actionNames.banned',
    'ui.moderation.actionNames.muted',
    'ui.moderation.actionNames.warned',
    'ui.moderation.actionNouns.ban',
    'ui.moderation.actionNouns.mute',
    'ui.moderation.actionSelect.placeholder',
    'ui.moderation.actions.ban.description',
    'ui.moderation.actions.ban.label',
    'ui.moderation.actions.blacklist.description',
    'ui.moderation.actions.blacklist.label',
    'ui.moderation.actions.delete.description',
    'ui.moderation.actions.delete.label',
    'ui.moderation.actions.mute.description',
    'ui.moderation.actions.mute.label',
    'ui.moderation.actions.unban.description',
    'ui.moderation.actions.unban.label',
    'ui.moderation.actions.unmute.description',
    'ui.moderation.actions.unmute.label',
    'ui.moderation.actions.warn.description',
    'ui.moderation.actions.warn.label',
    'ui.moderation.fields.reason',
    'ui.moderation.hubSelect.placeholder',
    'ui.moderation.hubSelection.description',
    'ui.moderation.hubSelection.fieldHubLabel',
    'ui.moderation.hubSelection.fieldHubPrompt',
    'ui.moderation.hubSelection.prompt',
    'ui.moderation.hubSelection.title',
    'ui.moderation.modal.duration.label',
    'ui.moderation.modal.duration.placeholder',
    'ui.moderation.modal.reason.label',
    'ui.moderation.modal.reason.placeholder',
    'ui.moderation.modal.title',
    'ui.moderation.prep.from',
    'ui.moderation.prep.in',
    'ui.moderation.targetSelection.description',
    'ui.moderation.targetSelection.serverField',
    'ui.moderation.targetSelection.title',
    'ui.moderation.targetSelection.userField',
    'ui.preferences.badges.buttons.hide',
    'ui.preferences.badges.buttons.show',
    'ui.preferences.badges.currentDescription',
    'ui.preferences.badges.description',
    'ui.preferences.badges.hidden',
    'ui.preferences.badges.title',
    'ui.preferences.badges.visible',
    'ui.preferences.buttons.return',
    'ui.preferences.description',
    'ui.preferences.errors.hide',
    'ui.preferences.errors.noPreferences',
    'ui.preferences.general.description',
    'ui.preferences.general.title',
    'ui.preferences.locale.description',
    'ui.preferences.locale.title',
    'ui.preferences.replyMentions.currentDescription',
    'ui.preferences.replyMentions.description',
    'ui.preferences.replyMentions.mentioned',
    'ui.preferences.replyMentions.notMentioned',
    'ui.preferences.replyMentions.title',
    'ui.preferences.select.category',
    'ui.preferences.select.placeholder',
    'ui.preferences.title',
    'ui.report.buttons.toHub',
    'ui.report.buttons.toStaff',
    'ui.report.modal.reason.label',
    'ui.report.modal.reason.placeholder',
    'ui.report.modal.toHub.title',
    'ui.report.modal.toStaff.title',
    'ui.report.status.ignoredBy',
    'ui.report.status.resolvedBy',
    'ui.setup.buttons.back',
    'ui.setup.buttons.cancel',
    'ui.setup.buttons.completeInfoFirst',
    'ui.setup.buttons.createHub',
    'ui.setup.buttons.discoverHubs',
    'ui.setup.buttons.enterHubInfo',
    'ui.setup.buttons.hubInfoComplete',
    'ui.setup.buttons.refresh',
    'ui.setup.buttons.supportServer',
    'ui.setup.embed.description',
    'ui.setup.select.chooseOption',
    'ui.setup.select.loadingDescription',
    'ui.setup.select.loadingLabel',
    'ui.setup.select.placeholder',
    'ui.staff.blacklist.descriptions.list',
    'ui.staff.blacklist.descriptions.noEntriesPage',
    'ui.staff.blacklist.descriptions.serverListEmpty',
    'ui.staff.blacklist.descriptions.userListEmpty',
    'ui.staff.blacklist.fields.added',
    'ui.staff.blacklist.fields.expires',
    'ui.staff.blacklist.fields.server',
    'ui.staff.blacklist.fields.staff',
    'ui.staff.blacklist.fields.user',
    'ui.staff.blacklist.labels.server',
    'ui.staff.blacklist.labels.user',
    'ui.staff.blacklist.titles.list',
    'ui.staff.blacklist.titles.searchResults',
    'ui.staff.blacklist.titles.serverList',
    'ui.staff.blacklist.titles.userList',
    'ui.staff.hub.badges.featured',
    'ui.staff.hub.badges.locked',
    'ui.staff.hub.badges.nsfw',
    'ui.staff.hub.badges.partnered',
    'ui.staff.hub.badges.private',
    'ui.staff.hub.badges.verified',
    'ui.staff.hub.fields.activity',
    'ui.staff.hub.fields.appealCooldown',
    'ui.staff.hub.fields.connections',
    'ui.staff.hub.fields.created',
    'ui.staff.hub.fields.description',
    'ui.staff.hub.fields.lastActive',
    'ui.staff.hub.fields.location',
    'ui.staff.hub.fields.messages',
    'ui.staff.hub.fields.name',
    'ui.staff.hub.fields.owner',
    'ui.staff.hub.fields.upvotes',
    'ui.staff.hub.fields.welcomeMessage',
    'ui.staff.hub.section.moderation',
    'ui.staff.hub.section.status',
    'ui.staff.hub.values.connectionsActive',
    'ui.staff.hub.values.messagesThisWeek',
    'ui.staff.hub.values.upvotesTotal',
    'ui.staff.server.fields.connections',
    'ui.staff.server.fields.created',
    'ui.staff.server.fields.inviteCode',
    'ui.staff.server.fields.lastMessage',
    'ui.staff.server.fields.messages',
    'ui.staff.server.fields.name',
    'ui.staff.server.fields.serverId',
    'ui.staff.server.fields.status',
    'ui.staff.server.fields.updated',
    'ui.staff.server.values.connectionsActive',
    'ui.staff.server.values.messagesTotal',
    'ui.staff.server.values.premium',
    'ui.staff.server.values.standard',
    'ui.staff.user.activity.achievements',
    'ui.staff.user.activity.appeals',
    'ui.staff.user.activity.reportsMade',
    'ui.staff.user.activity.reportsReceived',
    'ui.staff.user.activity.reviews',
    'ui.staff.user.content.antiSwearRules',
    'ui.staff.user.content.blockedWords',
    'ui.staff.user.content.hubs',
    'ui.staff.user.content.modPositions',
    'ui.staff.user.donation.expires',
    'ui.staff.user.donation.tier',
    'ui.staff.user.fields.created',
    'ui.staff.user.fields.engagement',
    'ui.staff.user.fields.hubJoins',
    'ui.staff.user.fields.lastMessage',
    'ui.staff.user.fields.lastVote',
    'ui.staff.user.fields.locale',
    'ui.staff.user.fields.messages',
    'ui.staff.user.fields.name',
    'ui.staff.user.fields.reputation',
    'ui.staff.user.fields.status',
    'ui.staff.user.fields.userId',
    'ui.staff.user.fields.votes',
    'ui.staff.user.moderation.blacklistsIssued',
    'ui.staff.user.moderation.blacklistsReceived',
    'ui.staff.user.moderation.infractionsIssued',
    'ui.staff.user.moderation.infractionsReceived',
    'ui.staff.user.preferences.mentionOnReply',
    'ui.staff.user.preferences.showBadges',
    'ui.staff.user.preferences.showNsfwHubs',
    'ui.staff.user.sections.activity',
    'ui.staff.user.sections.badges',
    'ui.staff.user.sections.createdContent',
    'ui.staff.user.sections.donation',
    'ui.staff.user.sections.preferences',
    'ui.staff.user.values.hubJoinsTotal',
    'ui.staff.user.values.member',
    'ui.staff.user.values.messagesSent',
    'ui.staff.user.values.never',
    'ui.staff.user.values.notSet',
    'ui.staff.user.values.reputationPoints',
    'ui.staff.user.values.staff',
    'ui.staff.user.values.votesCast'
]

def t(key: LocaleKey, locale: str = "en", **kwargs) -> str: ...

