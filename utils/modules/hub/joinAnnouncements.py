from typing import TYPE_CHECKING, List
import discord
import asyncio
from sqlalchemy import select

from utils.constants import logger
from utils.modules.common.service_utils import retry_with_backoff
from utils.modules.core.db.models import Hub, Connection
from utils.modules.core.i18n import t

if TYPE_CHECKING:
    from main import Bot


async def broadcast_hub_join_announcement(
    bot: 'Bot',
    hub: Hub,
    joining_server: discord.Guild,
) -> bool:
    """Broadcast an announcement to all hub channels when a new server joins."""
    try:
        # Get hub's language with English fallback
        hub_language = hub.language or 'en'

        # Get all other connections in the hub (excluding the joining channel)
        async with bot.db.get_session() as session:
            stmt = select(Connection).where(
                Connection.hubId == hub.id,
                Connection.connected.is_(True),
            )
            connections = list((await session.execute(stmt)).scalars().all())

        if not connections:
            logger.debug(f'No other connections found in hub {hub.id} to announce to')
            return True

        # Get localized text
        title = t('responses.hubEvents.serverJoined.title', hub_language)
        description = t(
            'responses.hubEvents.serverJoined.description',
            hub_language,
            serverName=joining_server.name,
        )
        footer_text = t('responses.hubEvents.serverJoined.footer', hub_language, hubName=hub.name)

        content = f'### {title}\n\n{description}\n\n-# {footer_text}'

        # Send announcements to all other channels in parallel
        logger.info(
            f'Broadcasting join announcement for server {joining_server.name} '
            f'to {len(connections)} channels in hub {hub.id} ({hub.name})'
        )

        successful_sends = await _send_announcements_parallel(bot, connections, content, hub.name)

        logger.info(
            f'Hub join announcement: {successful_sends}/{len(connections)} '
            f'announcements sent successfully for server {joining_server.name} in hub {hub.name}'
        )

        return successful_sends > 0

    except Exception as e:
        logger.error(f'Failed to broadcast hub join announcement: {e}')
        return False


async def _send_announcements_parallel(
    bot: 'Bot', connections: List[Connection], content: str, hub_name: str
) -> int:
    """Send announcements to all connections in parallel."""

    @retry_with_backoff()
    async def send_single_announcement(connection: Connection) -> bool:
        """Send announcement to a single connection."""
        try:
            # Use the existing webhook URL from the connection
            if not connection.webhookURL:
                logger.warning(f'No webhook URL found for channel {connection.channelId}')
                return False

            # Create webhook and send announcement
            webhook = discord.Webhook.from_url(connection.webhookURL, session=bot.http_session)

            # Use thread_id if this is a thread connection
            thread_id = None
            if connection.parentId:
                thread_id = int(connection.channelId)

            await webhook.send(
                content=content,
                username=f'{hub_name} | Hub Announcement',
                thread=discord.Object(id=thread_id) if thread_id else discord.utils.MISSING,
                allowed_mentions=discord.AllowedMentions.none(),
                wait=False,
            )

            logger.debug(f'Sent join announcement to channel {connection.channelId}')
            return True

        except discord.NotFound:
            logger.warning(f'Webhook not found for channel {connection.channelId}, will recreate')
            return False
        except discord.HTTPException as e:
            logger.warning(f'HTTP error sending announcement to {connection.channelId}: {e}')
            return False
        except Exception as e:
            logger.error(f'Error sending announcement to {connection.channelId}: {e}')
            return False

    results = await asyncio.gather(
        *(send_single_announcement(conn) for conn in connections), return_exceptions=True
    )

    # Count successful sends
    successful_sends = sum(1 for result in results if result is True)

    return successful_sends
