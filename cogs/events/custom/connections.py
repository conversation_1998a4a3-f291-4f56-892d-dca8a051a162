from utils.modules.events.baseEventCog import BaseEventCog
from utils.modules.events.eventDecorator import hub_event_listener
from utils.modules.events.eventDispatcher import (
    HubEvent,
    HubEventType,
)
from utils.modules.hub.hubLogging import log_event
from utils.modules.hub.joinAnnouncements import broadcast_hub_join_announcement
from utils.modules.services.hubService import HubService
from utils.constants import logger


class ConnectionEvents(BaseEventCog):
    @hub_event_listener(HubEventType.CONNECTION_ADD)
    async def on_connection_add(self, event: HubEvent):
        await log_event(self.bot, event)

        # Broadcast join announcement to other hub channels
        try:
            async with self.bot.db.get_session() as session:
                hub_service = HubService(session)
                hub = await hub_service.get_hub_by_id(event.hub_id)

                if not hub:
                    logger.warning(f'Hub {event.hub_id} not found for join announcement')
                    return

                # Get the Discord server
                if not event.target_server_id:
                    logger.warning('No target server ID in connection event')
                    return

                server = self.bot.get_guild(int(event.target_server_id))
                if not server:
                    logger.warning(
                        f'Server {event.target_server_id} not found for join announcement'
                    )
                    return

                await broadcast_hub_join_announcement(
                    bot=self.bot,
                    hub=hub,
                    joining_server=server,
                )

                logger.info(
                    f'Broadcasted join announcement for server {server.name} in hub {hub.name}'
                )

        except Exception as e:
            logger.error(f'Failed to broadcast hub join announcement: {e}')

    @hub_event_listener(HubEventType.CONNECTION_REMOVE)
    async def on_connection_remove(self, event: HubEvent):
        """Handle server disconnection events."""
        await log_event(self.bot, event)


async def setup(bot):
    await bot.add_cog(ConnectionEvents(bot))
