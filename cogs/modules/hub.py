import json
from datetime import datetime
from typing import TYPE_CHECKING, Optional

import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from utils.modules.core.db.models import Connection, Hub, HubInvite
from utils.modules.core.i18n import t
from utils.modules.core.webhookCore import (
    fetch_or_create_webhook,
    get_and_cleanup_webhooks,
)
from utils.modules.hub.welcomeMessage import send_hub_welcome_message
from utils.modules.events.eventDispatcher import dispatch_connection_event, HubEventType
from utils.modules.errors.customDiscord import (
    InvalidInput,
    InvalidInvite,
    NotConnected,
    WebhookError,
)
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.services.permission_service import PermissionService
from utils.modules.services.hubService import HubService
from utils.modules.ui.AutoComplete import (
    hubm_autocomplete,
    hubo_autocomplete,
    hubp_autocomplete,
)
from utils.modules.ui.views.hubAnnounceViews import OpenView
from utils.modules.ui.views.hubConfig.hubConfigurationView import ConfigurationView
from utils.modules.common.embeds import CommonErrors, CommonSuccess
from utils.modules.ui.views.hubConfig.inviteManagementView import InviteView
from utils.modules.ui.views.hubCreateView import HubCreationView
from utils.modules.ui.views.hubView import Paginator
from utils.utils import check_user

if TYPE_CHECKING:
    from main import Bot


class Hubs(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot: Bot = bot
        self.constants = bot.constants

    @commands.hybrid_command(
        name='hubs', description='View InterChat hubs', extras={'category': 'Hubs'}
    )
    @check_user()
    async def hubs(self, ctx: commands.Context[commands.Bot]):
        await ctx.defer()
        locale = await self.get_locale(ctx)
        async with self.bot.db.get_session() as session:
            hub_service = HubService(session)
            hubs = await hub_service.get_public_hubs()

            if not hubs:
                embed = CommonErrors.no_hubs_found(self.bot, locale, self.constants.support_invite)
                return await ctx.send(embed=embed)

            data = []
            for hub in hubs:
                data.append(
                    {
                        'hubId': hub.id,
                        'hubName': hub.name,
                        'long': hub.description,
                        'short': hub.shortDescription,
                        'lastActive': hub.lastActive,
                    }
                )

            view = Paginator(self.bot, ctx.author, data, locale)
            await ctx.send(embed=view.get_embed(), view=view)

    @commands.hybrid_group()
    async def hub(self, ctx: commands.Context[commands.Bot]): ...

    @hub.command(
        name='create',
        description='Create an InterChat hub',
        extras={'category': 'Hubs'},
    )
    @check_user()
    async def create_hub(self, ctx: commands.Context[commands.Bot]):
        """Interactive hub creation command"""
        await ctx.defer()
        locale = await self.get_locale(ctx)
        embed = discord.Embed(
            title=t('ui.hub.title', locale=locale),
            description=(t('ui.hub.description', locale=locale)),
            color=self.constants.color,
        )
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.display_avatar.url)
        view = HubCreationView(self.bot, ctx.author, locale)
        msg = await ctx.send(embed=embed, view=view, ephemeral=True)
        view.message = msg

    @hub.command(
        name='configure',
        description='Configure your InterChat hub',
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    @check_user()
    async def configure(self, ctx: commands.Context[commands.Bot], *, hub: str):
        await ctx.defer()
        locale = await self.get_locale(ctx)

        async with self.bot.db.get_session() as session:
            hub_service = HubService(session)
            db_hub = await hub_service.get_hub_by_name(hub, include_private=True)

            if not db_hub:
                raise InvalidInput()

            perm_service = PermissionService(session)
            user_level = await perm_service.get_user_permission_from_hub(db_hub, str(ctx.author.id))
            options = []

            if user_level >= HubPermissionLevel.MANAGER:
                options += [
                    discord.SelectOption(
                        emoji=self.bot.emotes.house_icon,
                        label=t('ui.hub.config.options.general.label', locale),
                        description=t('ui.hub.config.options.general.description', locale),
                        value='hgeneral',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.rules_icon,
                        label=t('ui.hub.config.options.rules.label', locale),
                        description=t('ui.hub.config.options.rules.description', locale),
                        value='hrules',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.person_icon,
                        label=t('ui.hub.config.options.team.label', locale),
                        description=t('ui.hub.config.options.team.description', locale),
                        value='hperm',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.gear_icon,
                        label=t('ui.hub.config.options.modules.label', locale),
                        description=t('ui.hub.config.options.modules.description', locale),
                        value='hmodules',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.hash_icon,
                        label=t('ui.hub.config.options.logging.label', locale),
                        description=t('ui.hub.config.options.logging.description', locale),
                        value='hlogging',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.shield_alert_icon,
                        label='Profanity Filter',
                        description='Manage anti-swear rules, patterns, and whitelist',
                        value='hprofanity',
                    ),
                ]

            if user_level >= HubPermissionLevel.OWNER:
                options += [
                    discord.SelectOption(
                        emoji=self.bot.emotes.globe_icon,
                        label=t('ui.hub.config.options.transfer.label', locale),
                        description=t('ui.hub.config.options.transfer.description', locale),
                        value='transfer',
                    )
                ]

            if user_level >= HubPermissionLevel.MODERATOR:
                options.append(
                    discord.SelectOption(
                        emoji=self.bot.emotes.chat_icon,
                        label=t('ui.hub.config.options.announcements.label', locale),
                        description=t('ui.hub.config.options.announcements.description', locale),
                        value='hsannounce',
                    )
                )

            embed = discord.Embed(
                description=f'### {self.bot.emotes.gear_icon} {t("ui.hub.config.title", locale="en")}\n{t("ui.hub.config.description", locale="en")}',
                color=self.constants.color,
            )
            view = ConfigurationView(
                self.bot,
                ctx.author,
                db_hub,
                options,
                user_level,
                locale,
            )
            message = await ctx.send(embed=embed, view=view)
            view.message = message

    @hubo_autocomplete
    @check_user()
    async def delete_hub(self, ctx: commands.Context[commands.Bot], hub: str):
        await ctx.defer()

        locale = await self.get_locale(ctx)

        async with self.bot.db.get_session() as session:
            hub_service = HubService(session)
            result = await hub_service.get_hub_by_name(hub)

            # confirm here with result found above | What?
            await session.delete(result)
            await session.commit()

        embed = CommonSuccess.hub_deleted_success(self.bot, locale)
        await ctx.send(embed=embed, ephemeral=True)

    @commands.hybrid_command(
        name='connect', description='Join an InterChat hub', extras={'category': 'Hubs'}
    )
    @commands.has_permissions(manage_channels=True)
    @hubp_autocomplete
    @check_user()
    @commands.guild_only()
    async def connect(
        self,
        ctx: commands.Context[commands.Bot],
        hub: Optional[str] = None,
        invite: Optional[str] = None,
        channel: Optional[discord.TextChannel | discord.Thread] = None,
    ):
        try:
            await ctx.defer(ephemeral=False)
            locale = await self.get_locale(ctx)

            if hub is None and invite is None:
                raise InvalidInput()

            if not ctx.guild:
                raise commands.NoPrivateMessage()

            selected_channel = channel or ctx.channel
            if not isinstance(selected_channel, discord.abc.GuildChannel):
                raise commands.NoPrivateMessage()

            result_invite = None
            result_hub = None

            if invite:
                async with self.bot.db.get_session() as session:
                    stmt = select(HubInvite).where(HubInvite.code == invite)
                    result_invite = await session.scalar(stmt)

                    if not result_invite:
                        raise InvalidInvite()

                    if result_invite.expires and result_invite.expires <= datetime.now():
                        await session.delete(result_invite)
                        await session.commit()
                        raise InvalidInvite()

                    result_invite.uses += 1

                    hub_service = HubService(session)
                    result_hub = await hub_service.get_hub_by_id(result_invite.hubId)

                    if result_invite.maxUses == result_invite.uses:
                        await session.delete(result_invite)

                    await session.commit()

            elif hub:
                async with self.bot.db.get_session() as session:
                    hub_service = HubService(session)
                    result_hub = await hub_service.get_hub_by_name(hub)

            if result_hub is None:
                raise InvalidInput()

            async with self.bot.db.get_session() as session:
                existing_connection_stmt = select(Connection).where(
                    (Connection.hubId == result_hub.id) & (Connection.serverId == str(ctx.guild.id))
                )
                existing_connection = await session.scalar(existing_connection_stmt)

                if existing_connection:
                    embed = CommonErrors.already_connected_to_hub(self.bot, locale)
                    return await ctx.send(embed=embed, ephemeral=True)

            webhook = await fetch_or_create_webhook(self.bot, selected_channel)

            if not webhook:
                raise WebhookError()

            connection = Connection(
                hubId=result_hub.id,
                channelId=str(selected_channel.id),
                webhookURL=webhook.url,
                parentId=(
                    str(selected_channel.parent_id)
                    if isinstance(selected_channel, discord.Thread)
                    else None
                ),
                invite=result_invite.code if result_invite else None,
                serverId=str(ctx.guild.id),
                createdAt=datetime.now(),
                lastActive=datetime.now(),
            )

            try:
                async with self.bot.db.get_session() as session:
                    session.add(connection)
                    await session.commit()

            except Exception as e:
                error_str = str(e).lower()

                if 'duplicate key value violates unique constraint' in error_str:
                    embed = CommonErrors.already_connected_to_hub(self.bot, locale)
                    return await ctx.send(embed=embed, ephemeral=True)

                elif any(
                    keyword in error_str
                    for keyword in [
                        'serverid_fkey',
                        'foreign key',
                        'serverdata',
                        'connection_serverid_fkey',
                        'foreignkeyviolationerror',
                    ]
                ):
                    embed = CommonErrors.connection_failed(self.bot, locale)
                    return await ctx.send(embed=embed, ephemeral=True)

                elif any(
                    keyword in error_str for keyword in ['integrityerror', 'constraint', 'violates']
                ):
                    embed = CommonErrors.connection_failed(self.bot, locale)
                    return await ctx.send(embed=embed, ephemeral=True)

                else:
                    raise

            embed = CommonSuccess.hub_connection_success(
                self.bot, result_hub.name, locale, ctx.author, selected_channel
            )
            await ctx.send(embed=embed)

            await send_hub_welcome_message(self.bot, result_hub, selected_channel, ctx.author)

            # Dispatch connection event for hub join announcements
            await dispatch_connection_event(
                action_type=HubEventType.CONNECTION_ADD,
                hub_id=result_hub.id,
                hub_name=result_hub.name,
                server_id=str(ctx.guild.id),
                server_name=ctx.guild.name,
            )

        except json.JSONDecodeError:
            raise InvalidInput()

        except discord.Forbidden:
            raise discord.Forbidden('Missing Permissions')

    @commands.hybrid_command(
        name='disconnect',
        description='Leave an InterChat hub',
        extras={'category': 'Hubs'},
    )
    @commands.has_permissions(manage_channels=True)
    @check_user()
    @commands.guild_only()
    async def leave(self, ctx: commands.Context[commands.Bot]):
        await ctx.defer(ephemeral=False)
        locale = await self.get_locale(ctx)

        if not isinstance(ctx.channel, discord.abc.GuildChannel):
            raise commands.NoPrivateMessage()

        async with self.bot.db.get_session() as session:
            stmt = select(Connection).where(Connection.channelId == str(ctx.channel.id))
            result = await session.scalar(stmt)

            if not result:
                raise NotConnected()

            await session.delete(result)
            await session.commit()

        await get_and_cleanup_webhooks(self.bot, ctx.channel)

        embed = CommonSuccess.hub_disconnect_success(self.bot, locale, ctx.author, ctx.channel)
        await ctx.send(embed=embed)

    @hub.command(
        name='invites',
        description='View all active invites for a hub',
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    @check_user()
    async def invites(self, ctx: commands.Context[commands.Bot], hub: str):
        await ctx.defer(ephemeral=False)
        locale = await self.get_locale(ctx)

        async with self.bot.db.get_session() as session:
            # Include private hubs since hubm_autocomplete already filters for user permissions
            hub_service = HubService(session)
            hub_obj = await hub_service.get_hub_by_name(hub, include_private=True)

            if not hub_obj:
                raise InvalidInput()

            # Load invites separately to avoid complex query modifications
            stmt = select(Hub).options(joinedload(Hub.invites)).where(Hub.id == hub_obj.id)
            hub_obj = await session.scalar(stmt)

            expired_invites: list[HubInvite] = []
            active_invites: list[HubInvite] = []

            current_time = datetime.now()

            for invite in hub_obj.invites:
                if invite.expires and invite.expires <= current_time:
                    expired_invites.append(invite)
                else:
                    active_invites.append(invite)

            if expired_invites:
                for expired_invite in expired_invites:
                    await session.delete(expired_invite)
                await session.commit()

        embed = discord.Embed(
            title=t('ui.hub.invites.title', locale), description=' ', color=self.constants.color
        )
        embed.set_footer(text=f'{t("responses.common.hub", locale)}: {hub}')

        if active_invites:
            for invite in active_invites:
                if invite.maxUses == 0:
                    muses = '∞'
                else:
                    muses = invite.maxUses
                expiry = f'<t:{int(invite.expires.timestamp())}:R>' if invite.expires else 'Never'
                embed.add_field(
                    name=f'{self.bot.emotes.link_icon} ||{invite.code}||',
                    value=(
                        f'\n> {t("ui.hub.invites.inviteUses", locale)} {invite.uses}/{muses}\n'
                        f'> {t("ui.hub.invites.inviteExpire", locale)} {expiry}',
                    ),
                    inline=True,
                )
        else:
            embed.description = f'{self.bot.emotes.x_icon} {t("ui.hub.invites.noneFound", locale)}'

        view = InviteView(self.bot, ctx.author, hub_obj)
        view.setup_button()
        await ctx.send(embed=embed, view=view, ephemeral=True)

    @hub.command(
        name='announce',
        description='Create an announcement to be sent within your hub',
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    @check_user()
    async def announce(self, ctx: commands.Context[commands.Bot], hub: str):
        await ctx.defer(ephemeral=False)
        locale = await self.get_locale(ctx)

        async with self.bot.db.get_session() as session:
            hub_service = HubService(session)
            # Include private hubs since hubm_autocomplete already filters for user permissions
            hub_obj = await hub_service.get_hub_by_name(hub, include_private=True)

            if not hub_obj:
                raise InvalidInput()

        embed = discord.Embed(
            title=t('ui.hub.announcements.title', locale),
            description=t('ui.hub.announcements.description', locale),
            color=self.constants.color,
        )
        view = OpenView(self.bot, ctx.author, hub_obj)
        await ctx.send(embed=embed, view=view, ephemeral=True)

    @hub.command(name='rules', description='View the rules for a hub', extras={'category': 'Hubs'})
    @check_user()
    async def rules(self, ctx: commands.Context[commands.Bot], hub: Optional[str] = None):
        """Display hub rules to members."""
        # If no hub specified, try to get hub from current channel
        locale = await self.get_locale(ctx)
        if not hub:
            if not ctx.guild:
                embed = CommonErrors.no_hub_specified(self.bot, locale)
                await ctx.send(embed=embed, ephemeral=True)
                return

            # Get hub from current channel connection
            async with self.bot.db.get_session() as session:
                stmt = (
                    select(Connection, Hub)
                    .join(Hub, Connection.hubId == Hub.id)
                    .where(
                        Connection.channelId == str(ctx.channel.id), Connection.connected.is_(True)
                    )
                )
                result = await session.execute(stmt)
                connection_data = result.tuples().first()

                if not connection_data:
                    embed = CommonErrors.not_connected_to_hub(self.bot, locale)
                    await ctx.send(embed=embed, ephemeral=True)
                    return

                _, hub_obj = connection_data
        else:
            # Get hub by name - include private hubs for potential moderator access
            async with self.bot.db.get_session() as session:
                hub_service = HubService(session)
                hub_obj = await hub_service.get_hub_by_name(hub, include_private=True)

                if not hub_obj:
                    embed = CommonErrors.hub_not_found_by_name(self.bot, locale, hub)
                    await ctx.send(embed=embed, ephemeral=True)
                    return

        # Create rules display embed
        await self._display_hub_rules(ctx, hub_obj)

    async def _display_hub_rules(self, ctx: commands.Context[commands.Bot], hub: Hub):
        """Display hub rules in a formatted embed."""
        locale = await self.get_locale(ctx)

        if not hub.rules or len(hub.rules) == 0:
            embed = discord.Embed(
                title=f'{self.bot.emotes.rules_icon} {t("commands.rules.title", locale="en", hubName=hub.name)}',
                description=t('commands.rules.noRules.description', locale),
                color=self.constants.color,
            )
        else:
            # Format rules as numbered list
            rules_text = ''
            for i, rule in enumerate(hub.rules, 1):
                rules_text += f'**{i}.** {rule}\n\n'

            # Truncate if too long for embed
            if len(rules_text) > 4000:
                rules_text = rules_text[:3950] + '...\n\n*Some rules truncated due to length.*'

            embed = discord.Embed(
                title=f'{self.bot.emotes.rules_icon} {t("commands.rules.title", locale="en", hubName=hub.name)}',
                description=rules_text,
                color=self.constants.color,
            )

            plural = 's' if len(hub.rules) != 1 else ''
            embed.set_footer(
                text=t('commands.rules.footer', locale, count=len(hub.rules), plural=plural)
            )

        await ctx.send(embed=embed)


async def setup(bot: 'Bot'):
    await bot.add_cog(Hubs(bot))
