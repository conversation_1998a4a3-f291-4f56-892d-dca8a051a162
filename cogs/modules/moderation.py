import json
from typing import TYPE_CHECKING, Optional, Sequence

import discord
from discord.ext import commands

from utils.modules.common.cogs import CogBase
from utils.modules.common.database import DatabaseQueries
from utils.modules.common.embeds import CommonErrors
from utils.modules.core.db.models import Hub
from utils.modules.core.i18n import t
from utils.modules.core.moderation import (
    fetch_original_message,
    fetch_original_msg_with_extra,
    get_user_moderated_hubs,
    mod_panel_embed,
)
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.services.hubService import HubService
from utils.modules.services.moderation.actionHandler import ModerationActionHandler
from utils.modules.services.moderation.types import ActionType, ModerationTarget
from utils.modules.services.moderationService import ModerationService
from utils.modules.services.permission_service import PermissionService
from utils.modules.ui.AutoComplete import hubm_autocomplete
from utils.modules.ui.views.moderation import (
    ActionHubSelectionView,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ModPanelView,
)
from utils.utils import parse_duration

if TYPE_CHECKING:
    from main import Bot


class Moderation(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = bot.constants
        self.locale = 'en'

    async def _fetch_hub(self, hub_id: str) -> Optional[Hub]:
        """Fetch hub by ID with moderators preloaded."""
        async with self.bot.db.get_session() as session:
            return await DatabaseQueries.fetch_hub_with_moderators(session, hub_id)

    async def _fetch_hub_by_name(self, hub_name: str) -> Optional[Hub]:
        """Fetch hub by name with moderators preloaded."""
        async with self.bot.db.get_session() as session:
            hub_service = HubService(session)
            return await hub_service.get_hub_by_name(hub_name, include_private=True)

    def _parse_hub_from_json(self, hub: str) -> Optional[dict[str, str]]:
        """Parse hub JSON string safely."""
        try:
            return json.loads(hub)
        except json.JSONDecodeError:
            return None

    async def _validate_and_get_hub(
        self, ctx: commands.Context, hub_name: Optional[str]
    ) -> Optional[Hub]:
        """Validate and fetch hub from JSON string."""
        async with self.bot.db.get_session() as session:
            hubId: Optional[str] = None
            if not ctx.interaction and ctx.message.reference and hub_name is None:
                original_msg = await fetch_original_message(
                    session, str(ctx.message.reference.message_id)
                )
                hubId = original_msg.hubId if original_msg else hubId

        selected_hub = (
            await self._fetch_hub_by_name(hub_name)
            if hub_name
            else await self._fetch_hub(hubId)
            if hubId
            else None
        )

        if not selected_hub:
            await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('ui.common.messages.hubNotFound', locale=self.locale),
                ephemeral=True,
            )
            return None

        return selected_hub

    def _parse_args_for_target_and_reason(
        self, args: str, action: ActionType
    ) -> tuple[Optional[str], Optional[int], str]:
        """Parse arguments to extract target type, duration, and reason."""
        if not args:
            return None, None, ''

        parts = args.split()
        duration_ms = None
        reason_parts = []

        i = 0

        # For mute action, check for duration
        if action == ActionType.MUTE and i < len(parts):
            try:
                duration_ms = parse_duration(parts[i])
                i += 1
            except ValueError:
                pass

        # Rest is reason
        if i < len(parts):
            reason_parts = parts[i:]

        reason = ' '.join(reason_parts) if reason_parts else None
        return reason, duration_ms, args

    @commands.hybrid_command(
        name='modpanel',
        description=t('commands.mod.panel.description', locale='en'),
        aliases=['p'],
        extras={'category': 'Moderation'},
    )
    async def panel(
        self,
        ctx: commands.Context[commands.Bot],
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
    ):
        """Open moderation panel."""
        await ctx.defer()

        # Auto-detect replied message if no message provided
        if not message and ctx.message.reference:
            ref = ctx.message.reference
            if ref and isinstance(ref.resolved, discord.Message):
                message = ref.resolved

        user_hubs = await get_user_moderated_hubs(self.bot, str(ctx.author.id))
        if not user_hubs:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.errors.noModeratedHubs', locale=self.locale),
                ephemeral=True,
            )

        if message:
            # Handle message-based moderation
            await self._handle_message_panel(ctx, message)
        else:
            # Handle user/server selection
            await self._handle_target_panel(ctx, user, server, user_hubs)

    @commands.hybrid_command(
        name='ban',
        description=t('commands.mod.ban.description', locale='en'),
        aliases=['b'],
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def ban(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        """Ban a user or server from a hub. Usage: reply to message with i.mod ban <reason> OR use slash cmd"""
        await self._execute_mod_action(ctx, ActionType.BAN, user, message, server, hub, reason)

    @commands.hybrid_command(
        name='mute',
        description=t('commands.mod.mute.description', locale='en'),
        aliases=['m'],
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def mute(
        self,
        ctx: commands.Context['Bot'],
        duration: str,
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        """Mute a user or server from a hub. Usage: reply to message with i.mod mute <duration> <reason> or use slash cmd"""
        await self._execute_mod_action(
            ctx, ActionType.MUTE, user, message, server, hub, f'{duration} {reason}'.strip()
        )

    @commands.hybrid_command(
        name='warn',
        description=t('commands.mod.warn.description', locale='en'),
        aliases=['w'],
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def warn(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        """Warn a user or server in a hub. Usage: reply to message with i.mod warn <reason> or use slash cmd"""
        await self._execute_mod_action(ctx, ActionType.WARN, user, message, server, hub, reason)

    @commands.hybrid_command(
        name='unmute',
        description=t('commands.mod.unmute.description', locale='en'),
        aliases=['um'],
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def unmute(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        """Unmute a user or server from a hub. Usage: reply to message with i.mod unmute OR use slash cmd"""
        await self._execute_mod_action(ctx, ActionType.UNMUTE, user, message, server, hub, reason)

    @commands.hybrid_command(
        name='unban',
        description=t('commands.mod.unban.description', locale='en'),
        aliases=['ub'],
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def unban(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        """Unban a user or server from a hub. Usage: i.mod unban OR reply to message with i.mod unban"""
        await self._execute_mod_action(ctx, ActionType.UNBAN, user, message, server, hub, reason)

    @commands.hybrid_command(
        name='delete_infraction',
        description=t('commands.mod.delete.description', locale='en'),
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def delete_infraction(
        self,
        ctx: commands.Context['Bot'],
        hub: Optional[str],
        infraction_id: str,
    ):
        """Delete an infraction (requires Manager+ permissions)."""
        await ctx.defer()

        selected_hub = await self._validate_and_get_hub(ctx, hub)
        if not selected_hub:
            return

        # Check Manager+ permission
        async with self.bot.db.get_session() as session:
            perm_service = PermissionService(session)
            has_perm, _ = await perm_service.check_permission_from_hub(
                selected_hub, str(ctx.author.id), HubPermissionLevel.MANAGER
            )
        if not has_perm:
            embed = CommonErrors.manager_permission_required(self.bot, self.locale)
            return await ctx.send(embed=embed, ephemeral=True)

        # Perform delete
        async with self.bot.db.get_session() as session:
            modsvc = ModerationService(session)
            success = await modsvc.delete_infraction(infraction_id)

        if not success:
            embed = CommonErrors.infraction_not_found(self.bot, self.locale)
            return await ctx.send(embed=embed, ephemeral=True)

        embed = discord.Embed(
            title=t('ui.common.titles.success', locale=self.locale),
            description=f'{self.bot.emotes.tick} {t("responses.infractions.delete.success", self.locale)}',
            color=discord.Color.green(),
        )
        await ctx.send(embed=embed, ephemeral=True)

    async def _execute_mod_action(
        self,
        ctx: commands.Context['Bot'],
        action: ActionType,
        user: Optional[discord.User],
        message: Optional[discord.Message],
        server: Optional[discord.Guild],
        hub_name: Optional[str],
        args: str,
    ):
        """New moderation action execution with message/user/server support."""
        await ctx.defer()

        # Auto-detect replied message if no message provided
        if not message and ctx.message.reference:
            ref = ctx.message.reference
            if ref and isinstance(ref.resolved, discord.Message):
                message = ref.resolved

        # If we have a message, extract information from it
        if message:
            await self._handle_message_mod_action(ctx, action, message, args)
            return

        # If no message but have user/server, proceed with traditional flow
        if user or server:
            # Get user's moderated hubs if no hub specified
            if not hub_name:
                user_hubs = await get_user_moderated_hubs(self.bot, str(ctx.author.id))
                if not user_hubs:
                    return await ctx.send(
                        f'{self.bot.emotes.x_icon} '
                        + t('responses.moderation.errors.noModeratedHubs', locale=self.locale),
                        ephemeral=True,
                    )

                # Show hub selection for user/server
                await self._handle_target_panel_with_action(
                    ctx, user, server, user_hubs, action, args
                )
                return

            # We have both target and hub, proceed directly
            selected_hub = await self._validate_and_get_hub(ctx, hub_name)
            if not selected_hub:
                return

            target = ModerationTarget(user=user, server=server)
            await self._execute_direct_action(ctx, action, target, selected_hub, args)
            return

        # No message, user, or server provided - show error
        embed = discord.Embed(
            title=t('ui.common.titles.error', locale=self.locale),
            description=f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.noTarget", self.locale)}',
            color=discord.Color.red(),
        )
        await ctx.send(embed=embed, ephemeral=True)

    async def _handle_message_mod_action(
        self,
        ctx: commands.Context,
        action: ActionType,
        message: discord.Message,
        args: str,
    ):
        """Handle moderation action based on a message."""
        async with self.bot.db.get_session() as session:
            msg_result = await fetch_original_msg_with_extra(session, str(message.id))

        if not msg_result:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.errors.originalMessageNotFound', locale=self.locale),
                ephemeral=True,
            )

        original_message, _, _ = msg_result

        try:
            target_user = await self.bot.fetch_user(int(original_message.authorId))
            target_server = await self.bot.fetch_guild(int(original_message.guildId))
        except (discord.NotFound, ValueError):
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.fetchAuthorOrServerFailed", self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        # Get the hub from the message
        selected_hub = await self._fetch_hub(original_message.hubId)
        if not selected_hub:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.hubNotFoundForMessage", self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        # For message-based actions, target the user by default, unless action indicates server
        # This follows the logic that when replying to a message, you're usually moderating the user
        target = ModerationTarget(user=target_user, server=None)

        # However, if the args contain "server" keyword, target the server instead
        if 'server' in args.lower():
            target = ModerationTarget(user=None, server=target_server)
            # Remove "server" from args so it doesn't become part of the reason
            args = args.replace('server', '').replace('Server', '').replace('SERVER', '').strip()

        await self._execute_direct_action(ctx, action, target, selected_hub, args)

    async def _handle_target_panel_with_action(
        self,
        ctx: commands.Context,
        user: Optional[discord.User],
        server: Optional[discord.Guild],
        user_hubs: Sequence[Hub],
        action: ActionType,
        args: str,
    ):
        """Handle hub selection for user/server with pending action."""
        if user and server:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.target.both", self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        # Create action-aware hub selection view
        view = ActionHubSelectionView(
            self.bot,
            ctx.author,
            user,
            server,
            user_hubs,
            action.value,
            args,
            self.locale,
        )

        embed = discord.Embed(
            title=t('ui.moderation.hubSelection.title', locale=self.locale),
            description=t('ui.moderation.hubSelection.description', locale=self.locale),
            color=self.constants.color,
        )

        if user:
            embed.add_field(
                name=t('ui.moderation.targetSelection.userField', locale=self.locale),
                value=f'{user.mention} (`{user.id}`)',
                inline=True,
            )
        if server:
            embed.add_field(
                name=t('ui.moderation.targetSelection.serverField', locale=self.locale),
                value=f'**{server.name}** (`{server.id}`)',
                inline=True,
            )

        # Add action info
        action_name = action.value.title()
        embed.add_field(
            name='Action',
            value=f'**{action_name}**',
            inline=True,
        )

        embed.add_field(
            name=t('ui.moderation.hubSelection.fieldHubLabel', locale=self.locale),
            value=t('ui.moderation.hubSelection.fieldHubPrompt', locale=self.locale),
            inline=False,
        )

        await ctx.send(embed=embed, view=view)

    async def _execute_direct_action(
        self,
        ctx: commands.Context,
        action: ActionType,
        target: ModerationTarget,
        hub: Hub,
        args: str,
    ):
        """Execute the moderation action directly with the provided target and hub."""
        # Parse args for duration and reason
        reason, duration_ms, _ = self._parse_args_for_target_and_reason(args, action)

        # Validate duration for mute action
        if action == ActionType.MUTE:
            if duration_ms is None:
                embed = discord.Embed(
                    title=t('ui.common.titles.error', locale=self.locale),
                    description=f'{self.bot.emotes.x_icon} Duration is required for mute actions. Use format like "1d", "2h", "30m".',
                    color=discord.Color.red(),
                )
                return await ctx.send(embed=embed, ephemeral=True)

        # Execute the action
        handler = ModerationActionHandler(self.bot, ctx.author, hub, self.locale)

        if action in [ActionType.WARN, ActionType.MUTE, ActionType.BAN]:
            await handler.handle_punitive_action(ctx, action, target, reason, duration_ms)
        elif action in [ActionType.UNMUTE, ActionType.UNBAN]:
            await handler.handle_revoke_action(ctx, action, target)

    async def _handle_message_panel(self, ctx: commands.Context, message: discord.Message):
        """Handle moderation panel for a specific message."""
        async with self.bot.db.get_session() as session:
            msg_result = await fetch_original_msg_with_extra(session, str(message.id))

        if not msg_result:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.errors.originalMessageNotFound', locale=self.locale),
                ephemeral=True,
            )

        original_message, _, _ = msg_result

        try:
            target_user = await self.bot.fetch_user(int(original_message.authorId))
            target_server = await self.bot.fetch_guild(int(original_message.guildId))
        except (discord.NotFound, ValueError):
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.fetchAuthorOrServerFailed", self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        # Get the hub from the message
        selected_hub = await self._fetch_hub(original_message.hubId)
        if not selected_hub:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.hubNotFoundForMessage", self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        # Show the mod panel directly
        view = ModPanelView(
            self.bot,
            ctx.author,
            target_user,
            target_server,
            message,
            selected_hub,
            self.locale,
        )

        embed = mod_panel_embed(
            self.bot,
            selected_hub,
            target_user,
            target_server,
            message,
            original_message.content,
            user_infractions=69,
            server_infractions=69,
            _locale=self.locale,
        )
        await ctx.send(embed=embed, view=view)

    async def _handle_target_panel(
        self,
        ctx: commands.Context,
        user: Optional[discord.User],
        server: Optional[discord.Guild],
        user_hubs: list,
    ):
        """Handle moderation panel for user/server targets."""
        if user and server:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.target.both", self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        # Show hub selection view
        view = HubSelectionView(
            self.bot,
            ctx.author,
            user,
            server,
            None,
            user_hubs,
            self.locale,
        )

        embed = discord.Embed(
            title=t('ui.moderation.hubSelection.title', locale=self.locale),
            description=t('ui.moderation.hubSelection.description', locale=self.locale),
            color=self.constants.color,
        )

        if user:
            embed.add_field(
                name=t('ui.moderation.targetSelection.userField', locale=self.locale),
                value=f'{user.mention} (`{user.id}`)',
                inline=True,
            )
        if server:
            embed.add_field(
                name=t('ui.moderation.targetSelection.serverField', locale=self.locale),
                value=f'**{server.name}** (`{server.id}`)',
                inline=True,
            )

        embed.add_field(
            name=t('ui.moderation.hubSelection.fieldHubLabel', locale=self.locale),
            value=t('ui.moderation.hubSelection.fieldHubPrompt', locale=self.locale),
            inline=False,
        )

        await ctx.send(embed=embed, view=view)


async def setup(bot: 'Bot'):
    await bot.add_cog(Moderation(bot))
